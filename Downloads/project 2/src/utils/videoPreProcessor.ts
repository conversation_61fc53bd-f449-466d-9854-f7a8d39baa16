import { createClient } from '@supabase/supabase-js';
import { initializePoseDetection, analyzePose, PoseAnalysis, PoseAnalysis3D } from './poseAnalysis';
import { ActivityType, VideoType } from '../types';

// Enhanced video metadata extraction with accurate FPS detection
interface VideoMetadataExtended {
  duration: number;
  width: number;
  height: number;
  fps: number;
  actualFps?: number;
  fpsSource: 'detected' | 'estimated' | 'fallback';
  codec?: string;
  bitrate?: number;
}

// Accurate FPS extraction using multiple detection methods
const extractAccurateFPS = async (file: File): Promise<{ fps: number; source: string; confidence: number }> => {
  console.log('🔍 Starting accurate FPS extraction for:', file.name);

  try {
    // Method 1: Try to extract FPS from video element with enhanced detection
    const videoFPS = await detectVideoElementFPS(file);
    if (videoFPS.fps > 0) {
      console.log(`✅ FPS detected via video element: ${videoFPS.fps} (confidence: ${videoFPS.confidence})`);
      return videoFPS;
    }

    // Method 2: Estimate FPS from frame timing (for iPhone videos)
    const estimatedFPS = await estimateFPSFromFrameTiming(file);
    if (estimatedFPS.fps > 0) {
      console.log(`✅ FPS estimated from frame timing: ${estimatedFPS.fps} (confidence: ${estimatedFPS.confidence})`);
      return estimatedFPS;
    }

    // Method 3: Use common iPhone video FPS values as intelligent fallback
    const intelligentFallback = getIntelligentFPSFallback(file);
    console.log(`⚠️ Using intelligent fallback FPS: ${intelligentFallback.fps} (confidence: ${intelligentFallback.confidence})`);
    return intelligentFallback;

  } catch (error) {
    console.error('❌ FPS extraction failed, using default fallback:', error);
    return { fps: 30, source: 'fallback_error', confidence: 0.1 };
  }
};

// Method 1: Enhanced video element FPS detection
const detectVideoElementFPS = async (file: File): Promise<{ fps: number; source: string; confidence: number }> => {
  return new Promise((resolve) => {
    const video = document.createElement('video');
    video.preload = 'metadata';

    video.onloadedmetadata = () => {
      // Try to extract FPS from video properties (limited browser support)
      const videoTracks = (video as any).videoTracks;
      if (videoTracks && videoTracks.length > 0) {
        const track = videoTracks[0];
        if (track.frameRate) {
          resolve({ fps: track.frameRate, source: 'video_track', confidence: 0.9 });
          return;
        }
      }

      // Fallback: No direct FPS available from video element
      resolve({ fps: 0, source: 'video_element_failed', confidence: 0 });
    };

    video.onerror = () => resolve({ fps: 0, source: 'video_element_error', confidence: 0 });
    video.src = URL.createObjectURL(file);
  });
};

// Method 2: Estimate FPS from frame timing analysis
const estimateFPSFromFrameTiming = async (file: File): Promise<{ fps: number; source: string; confidence: number }> => {
  return new Promise((resolve) => {
    const video = document.createElement('video');
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      resolve({ fps: 0, source: 'canvas_error', confidence: 0 });
      return;
    }

    video.preload = 'metadata';

    video.onloadedmetadata = async () => {
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      try {
        // Sample frames at different timestamps to estimate FPS
        const samplePoints = [0.1, 0.2, 0.3, 0.4, 0.5]; // Sample at 10%, 20%, etc.
        const frameDurations: number[] = [];

        for (let i = 0; i < samplePoints.length - 1; i++) {
          const time1 = video.duration * samplePoints[i];
          const time2 = video.duration * samplePoints[i + 1];

          video.currentTime = time1;
          await waitForVideoSeek(video);

          video.currentTime = time2;
          await waitForVideoSeek(video);

          // Estimate frame duration based on seeking behavior
          const timeDiff = time2 - time1;
          frameDurations.push(timeDiff);
        }

        if (frameDurations.length > 0) {
          const avgFrameDuration = frameDurations.reduce((a, b) => a + b, 0) / frameDurations.length;
          const estimatedFPS = Math.round(1 / (avgFrameDuration / 10)); // Rough estimation

          // Validate against common video FPS values
          const commonFPS = [24, 25, 30, 50, 60, 120];
          const closestFPS = commonFPS.reduce((prev, curr) =>
            Math.abs(curr - estimatedFPS) < Math.abs(prev - estimatedFPS) ? curr : prev
          );

          if (Math.abs(closestFPS - estimatedFPS) <= 5) {
            resolve({ fps: closestFPS, source: 'frame_timing_estimation', confidence: 0.7 });
            return;
          }
        }

        resolve({ fps: 0, source: 'frame_timing_failed', confidence: 0 });
      } catch (error) {
        resolve({ fps: 0, source: 'frame_timing_error', confidence: 0 });
      }
    };

    video.onerror = () => resolve({ fps: 0, source: 'video_load_error', confidence: 0 });
    video.src = URL.createObjectURL(file);
  });
};

// Method 3: Intelligent fallback based on file characteristics
const getIntelligentFPSFallback = (file: File): { fps: number; source: string; confidence: number } => {
  const fileName = file.name.toLowerCase();
  const fileSize = file.size;

  // iPhone video detection patterns
  const isIPhoneVideo = fileName.includes('img_') || fileName.includes('mov') ||
                       fileName.includes('iphone') || file.type.includes('quicktime');

  // File size analysis for FPS estimation
  const fileSizeMB = fileSize / (1024 * 1024);

  if (isIPhoneVideo) {
    // iPhone videos commonly use 30 or 60 FPS
    // Larger files often indicate higher FPS
    if (fileSizeMB > 100) {
      return { fps: 60, source: 'iphone_large_file_heuristic', confidence: 0.6 };
    } else {
      return { fps: 30, source: 'iphone_standard_heuristic', confidence: 0.5 };
    }
  }

  // General video fallback
  if (fileName.includes('60fps') || fileName.includes('60p')) {
    return { fps: 60, source: 'filename_heuristic', confidence: 0.7 };
  }

  if (fileName.includes('30fps') || fileName.includes('30p')) {
    return { fps: 30, source: 'filename_heuristic', confidence: 0.7 };
  }

  // Default fallback
  return { fps: 30, source: 'default_fallback', confidence: 0.3 };
};

// Helper function for video seeking
const waitForVideoSeek = (video: HTMLVideoElement): Promise<void> => {
  return new Promise((resolve) => {
    const onSeeked = () => {
      video.removeEventListener('seeked', onSeeked);
      resolve();
    };
    video.addEventListener('seeked', onSeeked);
  });
};

// URL-based FPS estimation for already uploaded videos
const estimateFPSFromURL = (videoUrl: string, metadata: { duration: number; width: number; height: number }): { fps: number; source: string; confidence: number } => {
  const url = videoUrl.toLowerCase();

  // Check URL patterns for FPS hints
  if (url.includes('60fps') || url.includes('60p')) {
    return { fps: 60, source: 'url_pattern_60fps', confidence: 0.8 };
  }

  if (url.includes('30fps') || url.includes('30p')) {
    return { fps: 30, source: 'url_pattern_30fps', confidence: 0.8 };
  }

  if (url.includes('24fps') || url.includes('24p')) {
    return { fps: 24, source: 'url_pattern_24fps', confidence: 0.8 };
  }

  // Analyze video characteristics for FPS estimation
  const isPortrait = metadata.height > metadata.width;
  const aspectRatio = metadata.width / metadata.height;

  // iPhone/mobile video patterns
  if (isPortrait && aspectRatio < 0.7) {
    // Typical iPhone portrait video
    if (metadata.width >= 1080) {
      return { fps: 60, source: 'mobile_hd_portrait_heuristic', confidence: 0.6 };
    } else {
      return { fps: 30, source: 'mobile_standard_portrait_heuristic', confidence: 0.5 };
    }
  }

  // High resolution videos often use higher FPS
  if (metadata.width >= 1920 || metadata.height >= 1080) {
    return { fps: 60, source: 'high_resolution_heuristic', confidence: 0.5 };
  }

  // Standard definition fallback
  return { fps: 30, source: 'url_standard_fallback', confidence: 0.3 };
};

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL as string;
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY as string;
const supabase = createClient(supabaseUrl, supabaseKey);

export interface VideoMetadata {
  filename: string;
  filePath: string;
  fileSize: number;
  duration: number;
  width: number;
  height: number;
  fps: number;
  activityType: ActivityType;
  viewType: VideoType;
}

export interface PreProcessingProgress {
  videoId: string;
  sessionId: string;
  totalFrames: number;
  processedFrames: number;
  successfulDetections: number;
  currentFrame: number;
  progress: number; // 0-100
  status: 'initializing' | 'processing' | 'completed' | 'failed';
  error?: string;
}

export interface PreProcessingResult {
  videoId: string;
  sessionId: string;
  totalFrames: number;
  successfulDetections: number;
  detectionRate: number;
  averageMetrics: {
    hipAngle: number;
    kneeAngle: number;
    ankleAngle: number;
    trunkAngle: number;
    neckAngle: number;
    strideLength: number;
    postureScore: number;
  };
  processingTimeSeconds: number;
  recommendations: Array<{
    category: string;
    priority: string;
    title: string;
    description: string;
    confidence: number;
  }>;
}

export class VideoPreProcessor {
  private video: HTMLVideoElement | null = null;
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;
  private onProgress?: (progress: PreProcessingProgress) => void;

  constructor(onProgress?: (progress: PreProcessingProgress) => void) {
    this.onProgress = onProgress;
  }

  async preProcessVideo(
    videoFile: File,
    activityType: ActivityType,
    viewType: VideoType
  ): Promise<PreProcessingResult> {
    const startTime = Date.now();

    try {
      // 1. Upload video and create database record
      const videoMetadata = await this.uploadAndCreateVideoRecord(videoFile, activityType, viewType);

      // 2. Initialize pose detection
      await initializePoseDetection();

      // 3. Create analysis session
      const sessionId = await this.createAnalysisSession(videoMetadata.videoId, videoMetadata, 1.78);

      // 4. Process video frame by frame
      const analysisResult = await this.processVideoFrames(videoMetadata, sessionId, 1.78);

      // 5. Calculate performance metrics and recommendations
      await this.calculatePerformanceMetrics(sessionId, analysisResult);
      const recommendations = await this.generateRecommendations(sessionId, analysisResult);

      // 6. Update session with final results
      await this.updateAnalysisSession(sessionId, analysisResult);

      // 7. Mark video as completed
      await this.updateVideoStatus(videoMetadata.videoId, 'completed');

      const processingTimeSeconds = (Date.now() - startTime) / 1000;

      return {
        videoId: videoMetadata.videoId,
        sessionId,
        totalFrames: analysisResult.totalFrames,
        successfulDetections: analysisResult.successfulDetections,
        detectionRate: analysisResult.detectionRate,
        averageMetrics: analysisResult.averageMetrics,
        processingTimeSeconds,
        recommendations
      };

    } catch (error) {
      console.error('Pre-processing failed:', error);
      throw error;
    } finally {
      this.cleanup();
    }
  }

  async processUploadedVideo(
    videoUrl: string,
    activityType: ActivityType,
    viewType: VideoType
  ): Promise<PreProcessingResult> {
    const startTime = Date.now();

    try {
      // 1. Create a minimal video record for the already-uploaded video
      const videoMetadata = await this.createVideoRecordFromUrl(videoUrl, activityType, viewType);

      // 2. Initialize pose detection
      await initializePoseDetection();

      // 3. Create analysis session
      const sessionId = await this.createAnalysisSession(videoMetadata.videoId, videoMetadata);

      // 4. Process video frame by frame directly from URL
      const analysisResult = await this.processVideoFramesFromUrl(videoUrl, sessionId);

      // 5. Calculate performance metrics and recommendations
      await this.calculatePerformanceMetrics(sessionId, analysisResult);
      const recommendations = await this.generateRecommendations(sessionId, analysisResult);

      // 6. Update session with final results
      await this.updateAnalysisSession(sessionId, analysisResult);

      // 7. Mark video as completed
      await this.updateVideoStatus(videoMetadata.videoId, 'completed');

      const processingTimeSeconds = (Date.now() - startTime) / 1000;

      return {
        videoId: videoMetadata.videoId,
        sessionId,
        totalFrames: analysisResult.totalFrames,
        successfulDetections: analysisResult.successfulDetections,
        detectionRate: analysisResult.detectionRate,
        averageMetrics: analysisResult.averageMetrics,
        processingTimeSeconds,
        recommendations
      };

    } catch (error) {
      console.error('Processing uploaded video failed:', error);
      throw error;
    } finally {
      this.cleanup();
    }
  }

  private async uploadAndCreateVideoRecord(
    file: File,
    activityType: ActivityType,
    viewType: VideoType
  ): Promise<VideoMetadata & { videoId: string }> {
    // Upload to Supabase storage
    const timestamp = new Date().getTime();
    const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'mp4';
    const folderPath = `${activityType}-${viewType}`;
    const filePath = `${folderPath}/video_${timestamp}.${fileExtension}`;

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('videos')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) throw uploadError;

    // Log successful upload with metadata for debugging and verification
    console.log('✅ Video upload successful:', {
      uploadPath: uploadData?.path,
      fullPath: uploadData?.fullPath,
      filePath: filePath,
      fileSize: (file.size / (1024 * 1024)).toFixed(1) + 'MB',
      uploadId: uploadData?.id
    });

    // Get video metadata
    const videoMetadata = await this.extractVideoMetadata(file);

    // Create database record
    const { data: videoRecord, error: dbError } = await supabase
      .from('pose_videos')
      .insert({
        filename: file.name,
        file_path: filePath,
        file_size: file.size,
        duration: videoMetadata.duration,
        width: videoMetadata.width,
        height: videoMetadata.height,
        fps: videoMetadata.fps,
        activity_type: activityType,
        view_type: viewType,
        processing_status: 'processing',
        processing_started_at: new Date().toISOString()
      })
      .select()
      .single();

    if (dbError) throw dbError;

    return {
      videoId: videoRecord.id,
      filename: file.name,
      filePath,
      fileSize: file.size,
      duration: videoMetadata.duration,
      width: videoMetadata.width,
      height: videoMetadata.height,
      fps: videoMetadata.fps,
      activityType,
      viewType
    };
  }

  private async extractVideoMetadata(file: File): Promise<VideoMetadataExtended> {
    console.log('🎬 Extracting enhanced video metadata with accurate FPS detection for:', file.name);

    // Get basic metadata first
    const basicMetadata = await new Promise<{ duration: number; width: number; height: number }>((resolve, reject) => {
      const video = document.createElement('video');
      video.preload = 'metadata';

      video.onloadedmetadata = () => {
        resolve({
          duration: video.duration,
          width: video.videoWidth,
          height: video.videoHeight
        });
      };

      video.onerror = () => reject(new Error('Failed to load basic video metadata'));
      video.src = URL.createObjectURL(file);
    });

    // Extract accurate FPS using multiple detection methods
    const fpsResult = await extractAccurateFPS(file);

    // Create enhanced metadata with detailed FPS tracking
    const enhancedMetadata: VideoMetadataExtended = {
      duration: basicMetadata.duration,
      width: basicMetadata.width,
      height: basicMetadata.height,
      fps: fpsResult.fps,
      actualFps: fpsResult.confidence > 0.8 ? fpsResult.fps : undefined,
      fpsSource: fpsResult.source.includes('detected') ? 'detected' :
                 fpsResult.source.includes('estimation') ? 'estimated' : 'fallback',
      // Note: codec and bitrate would require additional video analysis libraries
      codec: undefined, // Could be implemented with ffprobe.js or similar
      bitrate: undefined // Could be estimated from file size and duration
    };

    console.log('📊 Enhanced video metadata extraction complete:', {
      filename: file.name,
      duration: enhancedMetadata.duration.toFixed(2) + 's',
      dimensions: `${enhancedMetadata.width}x${enhancedMetadata.height}`,
      fps: enhancedMetadata.fps,
      actualFps: enhancedMetadata.actualFps,
      fpsSource: enhancedMetadata.fpsSource,
      fps_confidence: fpsResult.confidence,
      file_size_mb: (file.size / (1024 * 1024)).toFixed(1) + 'MB',
      is_portrait: enhancedMetadata.height > enhancedMetadata.width,
      aspect_ratio: (enhancedMetadata.width / enhancedMetadata.height).toFixed(2)
    });

    return enhancedMetadata;
  }

  private async createVideoRecordFromUrl(
    videoUrl: string,
    activityType: ActivityType,
    viewType: VideoType
  ): Promise<VideoMetadata & { videoId: string }> {
    // Extract metadata from the video URL
    const videoMetadata = await this.extractVideoMetadataFromUrl(videoUrl);

    // Create database record
    const { data: videoRecord, error: dbError } = await supabase
      .from('pose_videos')
      .insert({
        filename: `uploaded_${activityType}_${viewType}_video.mp4`,
        file_path: videoUrl, // Store the full URL as the file path
        file_size: 0, // Unknown for already uploaded videos
        duration: videoMetadata.duration,
        width: videoMetadata.width,
        height: videoMetadata.height,
        fps: videoMetadata.fps,
        activity_type: activityType,
        view_type: viewType,
        processing_status: 'processing',
        processing_started_at: new Date().toISOString()
      })
      .select()
      .single();

    if (dbError) throw dbError;

    return {
      videoId: videoRecord.id,
      filename: `uploaded_${activityType}_${viewType}_video.mp4`,
      filePath: videoUrl,
      fileSize: 0,
      duration: videoMetadata.duration,
      width: videoMetadata.width,
      height: videoMetadata.height,
      fps: videoMetadata.fps,
      activityType,
      viewType
    };
  }

  private async extractVideoMetadataFromUrl(videoUrl: string): Promise<Omit<VideoMetadata, 'filename' | 'filePath' | 'fileSize' | 'activityType' | 'viewType'>> {
    console.log('🌐 Extracting video metadata from URL with FPS detection:', videoUrl);

    // Get basic metadata first
    const basicMetadata = await new Promise<{ duration: number; width: number; height: number }>((resolve, reject) => {
      const video = document.createElement('video');
      video.preload = 'metadata';
      video.crossOrigin = 'anonymous';

      video.onloadedmetadata = () => {
        resolve({
          duration: video.duration,
          width: video.videoWidth,
          height: video.videoHeight
        });
      };

      video.onerror = () => reject(new Error('Failed to load video metadata from URL'));
      video.src = videoUrl;
    });

    // For URL-based videos, we can't use file-based FPS detection
    // Use intelligent estimation based on video characteristics
    const estimatedFPS = estimateFPSFromURL(videoUrl, basicMetadata);

    console.log('📊 URL video metadata extraction complete:', {
      url: videoUrl,
      duration: basicMetadata.duration.toFixed(2) + 's',
      dimensions: `${basicMetadata.width}x${basicMetadata.height}`,
      fps: estimatedFPS.fps,
      fps_source: estimatedFPS.source,
      fps_confidence: estimatedFPS.confidence,
      is_portrait: basicMetadata.height > basicMetadata.width,
      aspect_ratio: (basicMetadata.width / basicMetadata.height).toFixed(2)
    });

    return {
      duration: basicMetadata.duration,
      width: basicMetadata.width,
      height: basicMetadata.height,
      fps: estimatedFPS.fps // INTELLIGENT FPS ESTIMATION instead of hardcoded 30
    };
  }

  private async createAnalysisSession(videoId: string, metadata: VideoMetadata, userHeightMeters: number = 1.78): Promise<string> {
    const { data, error } = await supabase
      .from('pose_sessions')
      .insert({
        video_id: videoId,
        model_type: 'BlazePose',
        analysis_fps: Math.min(metadata.fps, 10), // Analyze at most 10 FPS
        user_height_meters: userHeightMeters
      })
      .select()
      .single();

    if (error) throw error;
    return data.id;
  }

  private async processVideoFrames(
    metadata: VideoMetadata & { videoId: string },
    sessionId: string,
    userHeightMeters: number = 1.78  // ADD this parameter for 3D calibration
  ): Promise<{
    totalFrames: number;
    successfulDetections: number;
    detectionRate: number;
    averageMetrics: any;
    frameData: Array<{ frameNumber: number; timestamp: number; analysis: PoseAnalysis | null }>;
  }> {
    // Create video element and canvas for processing
    this.video = document.createElement('video');
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d');

    if (!this.ctx) throw new Error('Failed to get canvas context');

    // Load video
    await this.loadVideo(metadata.filePath);

    this.canvas.width = this.video.videoWidth;
    this.canvas.height = this.video.videoHeight;

    const analysisFps = Math.min(metadata.fps, 10);
    const frameInterval = metadata.fps / analysisFps;
    const totalFrames = Math.floor(metadata.duration * analysisFps);

    const frameData: Array<{ frameNumber: number; timestamp: number; analysis: PoseAnalysis | null }> = [];
    let successfulDetections = 0;

    const metrics = {
      hipAngles: [] as number[],
      kneeAngles: [] as number[],
      ankleAngles: [] as number[],
      trunkAngles: [] as number[],
      neckAngles: [] as number[],
      strideLengths: [] as number[],
      postureScores: [] as number[]
    };

    for (let frameNumber = 0; frameNumber < totalFrames; frameNumber++) {
      const timestamp = (frameNumber * frameInterval) / metadata.fps;

      // Seek to frame
      this.video.currentTime = timestamp;
      await this.waitForSeek();

      // Draw frame to canvas
      this.ctx.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);

      try {
        // Analyze pose WITH HEIGHT calibration for 3D world coordinates
        const analysis = await analyzePose(this.canvas, userHeightMeters) as PoseAnalysis3D;
        frameData.push({ frameNumber, timestamp, analysis });

        // Store frame data in database
        await this.storePoseData(sessionId, frameNumber, timestamp, analysis);

        // MANDATORY: Verify 3D world coordinate data storage for first frame
        if (frameNumber === 0) {
          const { data: verifyData } = await supabase
            .from('pose_data')
            .select('hip_world_z, estimated_distance_meters, pixels_per_meter')
            .eq('session_id', sessionId)
            .eq('frame_number', 0)
            .single();

          console.log('✅ VERIFICATION: First frame 3D data stored successfully:', verifyData);
        }

        // Collect metrics
        metrics.hipAngles.push(analysis.angles.hip.angle);
        metrics.kneeAngles.push(analysis.angles.knee.angle);
        metrics.ankleAngles.push(analysis.angles.ankle.angle);
        metrics.trunkAngles.push(analysis.angles.trunk.angle);
        metrics.neckAngles.push(analysis.angles.neck.angle);
        metrics.strideLengths.push(analysis.metrics.strideLength);
        metrics.postureScores.push(analysis.metrics.postureScore);

        successfulDetections++;
      } catch (error) {
        console.warn(`Failed to analyze frame ${frameNumber}:`, error);
        frameData.push({ frameNumber, timestamp, analysis: null });

        // Store failed detection
        await this.storePoseData(sessionId, frameNumber, timestamp, null);
      }

      // Report progress
      if (this.onProgress) {
        this.onProgress({
          videoId: metadata.videoId,
          sessionId,
          totalFrames,
          processedFrames: frameNumber + 1,
          successfulDetections,
          currentFrame: frameNumber,
          progress: ((frameNumber + 1) / totalFrames) * 100,
          status: 'processing'
        });
      }
    }

    const averageMetrics = {
      hipAngle: this.average(metrics.hipAngles),
      kneeAngle: this.average(metrics.kneeAngles),
      ankleAngle: this.average(metrics.ankleAngles),
      trunkAngle: this.average(metrics.trunkAngles),
      neckAngle: this.average(metrics.neckAngles),
      strideLength: this.average(metrics.strideLengths),
      postureScore: this.average(metrics.postureScores)
    };

    return {
      totalFrames,
      successfulDetections,
      detectionRate: (successfulDetections / totalFrames) * 100,
      averageMetrics,
      frameData
    };
  }

  private async processVideoFramesFromUrl(
    videoUrl: string,
    sessionId: string
  ): Promise<{
    totalFrames: number;
    successfulDetections: number;
    detectionRate: number;
    averageMetrics: any;
    frameData: Array<{ frameNumber: number; timestamp: number; analysis: PoseAnalysis | null }>;
  }> {
    // Create video element and canvas for processing
    this.video = document.createElement('video');
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d');

    if (!this.ctx) throw new Error('Failed to get canvas context');

    // Load video directly from URL
    await this.loadVideoFromUrl(videoUrl);

    this.canvas.width = this.video.videoWidth;
    this.canvas.height = this.video.videoHeight;

    // Extract accurate FPS for URL-based video processing
    const videoMetadata = {
      duration: this.video.duration,
      width: this.video.videoWidth,
      height: this.video.videoHeight
    };
    const fpsResult = estimateFPSFromURL(videoUrl, videoMetadata);
    const fps = fpsResult.fps; // ACCURATE FPS instead of hardcoded 30

    console.log('🎬 URL video processing with accurate FPS:', {
      url: videoUrl,
      detected_fps: fps,
      fps_source: fpsResult.source,
      fps_confidence: fpsResult.confidence
    });

    const analysisFps = Math.min(fps, 10);
    const frameInterval = fps / analysisFps;
    const totalFrames = Math.floor(this.video.duration * analysisFps);

    const frameData: Array<{ frameNumber: number; timestamp: number; analysis: PoseAnalysis | null }> = [];
    let successfulDetections = 0;

    const metrics = {
      hipAngles: [] as number[],
      kneeAngles: [] as number[],
      ankleAngles: [] as number[],
      trunkAngles: [] as number[],
      neckAngles: [] as number[],
      strideLengths: [] as number[],
      postureScores: [] as number[]
    };

    for (let frameNumber = 0; frameNumber < totalFrames; frameNumber++) {
      const timestamp = (frameNumber * frameInterval) / fps;

      // Seek to frame
      this.video.currentTime = timestamp;
      await this.waitForSeek();

      // Draw frame to canvas
      this.ctx.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);

      try {
        // Analyze pose
        const analysis = await analyzePose(this.canvas);
        frameData.push({ frameNumber, timestamp, analysis });

        // Store frame data in database
        await this.storePoseData(sessionId, frameNumber, timestamp, analysis);

        // Collect metrics
        metrics.hipAngles.push(analysis.angles.hip.angle);
        metrics.kneeAngles.push(analysis.angles.knee.angle);
        metrics.ankleAngles.push(analysis.angles.ankle.angle);
        metrics.trunkAngles.push(analysis.angles.trunk.angle);
        metrics.neckAngles.push(analysis.angles.neck.angle);
        metrics.strideLengths.push(analysis.metrics.strideLength);
        metrics.postureScores.push(analysis.metrics.postureScore);

        successfulDetections++;
      } catch (error) {
        console.warn(`Failed to analyze frame ${frameNumber}:`, error);
        frameData.push({ frameNumber, timestamp, analysis: null });

        // Store failed detection
        await this.storePoseData(sessionId, frameNumber, timestamp, null);
      }

      // Report progress
      if (this.onProgress) {
        this.onProgress({
          videoId: 'url-based-video',
          sessionId,
          totalFrames,
          processedFrames: frameNumber + 1,
          successfulDetections,
          currentFrame: frameNumber,
          progress: ((frameNumber + 1) / totalFrames) * 100,
          status: 'processing'
        });
      }
    }

    const averageMetrics = {
      hipAngle: this.average(metrics.hipAngles),
      kneeAngle: this.average(metrics.kneeAngles),
      ankleAngle: this.average(metrics.ankleAngles),
      trunkAngle: this.average(metrics.trunkAngles),
      neckAngle: this.average(metrics.neckAngles),
      strideLength: this.average(metrics.strideLengths),
      postureScore: this.average(metrics.postureScores)
    };

    return {
      totalFrames,
      successfulDetections,
      detectionRate: (successfulDetections / totalFrames) * 100,
      averageMetrics,
      frameData
    };
  }

  private async loadVideo(filePath: string): Promise<void> {
    if (!this.video) throw new Error('Video element not initialized');

    const { data } = supabase.storage.from('videos').getPublicUrl(filePath);

    return new Promise((resolve, reject) => {
      this.video!.onloadeddata = () => resolve();
      this.video!.onerror = () => reject(new Error('Failed to load video'));
      this.video!.src = data.publicUrl;
    });
  }

  private async loadVideoFromUrl(videoUrl: string): Promise<void> {
    if (!this.video) throw new Error('Video element not initialized');

    return new Promise((resolve, reject) => {
      this.video!.onloadeddata = () => resolve();
      this.video!.onerror = () => reject(new Error('Failed to load video from URL'));
      this.video!.crossOrigin = 'anonymous';
      this.video!.src = videoUrl;
    });
  }

  private async waitForSeek(): Promise<void> {
    if (!this.video) return;

    return new Promise((resolve) => {
      const onSeeked = () => {
        this.video!.removeEventListener('seeked', onSeeked);
        resolve();
      };
      // Add null check before addEventListener
      if (this.video) {
        this.video.addEventListener('seeked', onSeeked);
      } else {
        resolve(); // Resolve immediately if video is null
      }
    });
  }

private async storePoseData(
  sessionId: string,
  frameNumber: number,
  timestamp: number,
  analysis: PoseAnalysis3D | null
): Promise<void> {
  const poseData = {
    session_id: sessionId,
    frame_number: frameNumber,
    timestamp_seconds: timestamp,
    pose_detected: analysis !== null,
    detection_confidence: analysis ? (analysis.metrics.detectionQuality / 100) : 0,

    // Existing 2D angle and position data (PRESERVE EXACTLY)
    hip_angle: analysis?.angles.hip.angle,
    knee_angle: analysis?.angles.knee.angle,
    ankle_angle: analysis?.angles.ankle.angle,
    trunk_angle: analysis?.angles.trunk.angle,
    neck_angle: analysis?.angles.neck.angle,
    hip_x: analysis?.angles.hip.position.x,
    hip_y: analysis?.angles.hip.position.y,
    knee_x: analysis?.angles.knee.position.x,
    knee_y: analysis?.angles.knee.position.y,
    ankle_x: analysis?.angles.ankle.position.x,
    ankle_y: analysis?.angles.ankle.position.y,
    trunk_x: analysis?.angles.trunk.position.x,
    trunk_y: analysis?.angles.trunk.position.y,
    neck_x: analysis?.angles.neck.position.x,
    neck_y: analysis?.angles.neck.position.y,

    // Enhanced anatomical keypoints (PRESERVE EXACTLY)
    shoulder_x: analysis?.keypoints.shoulder?.x,
    shoulder_y: analysis?.keypoints.shoulder?.y,
    elbow_x: analysis?.keypoints.elbow?.x,
    elbow_y: analysis?.keypoints.elbow?.y,
    wrist_x: analysis?.keypoints.wrist?.x,
    wrist_y: analysis?.keypoints.wrist?.y,
    heel_x: analysis?.keypoints.heel?.x,
    heel_y: analysis?.keypoints.heel?.y,
    foot_x: analysis?.keypoints.foot?.x,
    foot_y: analysis?.keypoints.foot?.y,

    // Bilateral keypoints (PRESERVE EXACTLY)
    shoulder_left_x: analysis?.keypoints.shoulderLeft?.x,
    shoulder_left_y: analysis?.keypoints.shoulderLeft?.y,
    shoulder_right_x: analysis?.keypoints.shoulderRight?.x,
    shoulder_right_y: analysis?.keypoints.shoulderRight?.y,
    elbow_left_x: analysis?.keypoints.elbowLeft?.x,
    elbow_left_y: analysis?.keypoints.elbowLeft?.y,
    elbow_right_x: analysis?.keypoints.elbowRight?.x,
    elbow_right_y: analysis?.keypoints.elbowRight?.y,
    wrist_left_x: analysis?.keypoints.wristLeft?.x,
    wrist_left_y: analysis?.keypoints.wristLeft?.y,
    wrist_right_x: analysis?.keypoints.wristRight?.x,
    wrist_right_y: analysis?.keypoints.wristRight?.y,
    hip_left_x: analysis?.keypoints.hipLeft?.x,
    hip_left_y: analysis?.keypoints.hipLeft?.y,
    hip_right_x: analysis?.keypoints.hipRight?.x,
    hip_right_y: analysis?.keypoints.hipRight?.y,
    knee_left_x: analysis?.keypoints.kneeLeft?.x,
    knee_left_y: analysis?.keypoints.kneeLeft?.y,
    knee_right_x: analysis?.keypoints.kneeRight?.x,
    knee_right_y: analysis?.keypoints.kneeRight?.y,
    ankle_left_x: analysis?.keypoints.ankleLeft?.x,
    ankle_left_y: analysis?.keypoints.ankleLeft?.y,
    ankle_right_x: analysis?.keypoints.ankleRight?.x,
    ankle_right_y: analysis?.keypoints.ankleRight?.y,

    // NEW: 3D world landmark coordinates in meters
    hip_world_x: analysis?.worldLandmarks?.hip?.x,
    hip_world_y: analysis?.worldLandmarks?.hip?.y,
    hip_world_z: analysis?.worldLandmarks?.hip?.z,
    knee_world_x: analysis?.worldLandmarks?.knee?.x,
    knee_world_y: analysis?.worldLandmarks?.knee?.y,
    knee_world_z: analysis?.worldLandmarks?.knee?.z,
    ankle_world_x: analysis?.worldLandmarks?.ankle?.x,
    ankle_world_y: analysis?.worldLandmarks?.ankle?.y,
    ankle_world_z: analysis?.worldLandmarks?.ankle?.z,
    shoulder_world_x: analysis?.worldLandmarks?.shoulder?.x,
    shoulder_world_y: analysis?.worldLandmarks?.shoulder?.y,
    shoulder_world_z: analysis?.worldLandmarks?.shoulder?.z,

    // NEW: Distance estimation and scaling metrics
    estimated_distance_meters: analysis?.estimatedDistanceMeters,
    depth_scale_factor: analysis?.depthScaleFactor,
    pixels_per_meter: analysis?.pixelsPerMeter,

    // NEW: World center of mass (using hip as reference point)
    world_center_x: analysis?.worldLandmarks?.hip?.x || 0,
    world_center_y: analysis?.worldLandmarks?.hip?.y || 0,
    world_center_z: analysis?.worldLandmarks?.hip?.z || 2,

    // Enhanced quality metrics (PRESERVE EXACTLY)
    detection_quality: analysis?.metrics.detectionQuality,
    bilateral_symmetry: analysis?.metrics.bilateralSymmetry,
    stride_length: analysis?.metrics.strideLength,
    foot_strike_type: analysis?.metrics.footStrike,
    posture_score: analysis?.metrics.postureScore,
    raw_keypoints: analysis?.keypoints ? JSON.stringify(analysis.keypoints) : null
  };

  // Debug logging for 3D data verification (every 30th frame to avoid spam)
  if (analysis && frameNumber % 30 === 0) {
    console.log(`🌐 3D Data Storage - Frame ${frameNumber}:`, {
      distance: poseData.estimated_distance_meters?.toFixed(2) + 'm',
      depthScale: poseData.depth_scale_factor?.toFixed(2),
      pixelsPerMeter: poseData.pixels_per_meter?.toFixed(1),
      hipWorldZ: poseData.hip_world_z?.toFixed(2) + 'm',
      has3DData: !!poseData.hip_world_z
    });
  }

  const { error } = await supabase
    .from('pose_data')
    .insert(poseData);

  if (error) {
    console.error('Failed to store pose data:', error);
  }
}

  private average(numbers: number[]): number {
    if (numbers.length === 0) return 0;
    return numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
  }

  private async calculatePerformanceMetrics(sessionId: string, analysisResult: any): Promise<void> {
    // Calculate advanced metrics for data science analysis
    const metrics = [
      {
        metric_name: 'stride_consistency',
        metric_category: 'efficiency',
        value: this.calculateStrideConsistency(analysisResult.frameData),
        unit: 'coefficient_of_variation',
        score: Math.max(0, 100 - (this.calculateStrideConsistency(analysisResult.frameData) * 100))
      },
      {
        metric_name: 'knee_drive_efficiency',
        metric_category: 'power',
        value: analysisResult.averageMetrics.kneeAngle,
        unit: 'degrees',
        score: this.scoreKneeDrive(analysisResult.averageMetrics.kneeAngle)
      },
      {
        metric_name: 'posture_stability',
        metric_category: 'form',
        value: analysisResult.averageMetrics.postureScore,
        unit: 'percentage',
        score: analysisResult.averageMetrics.postureScore
      }
    ];

    for (const metric of metrics) {
      await supabase.from('pose_metrics').insert({
        session_id: sessionId,
        ...metric,
        calculation_method: 'automated_analysis',
        confidence_level: 0.85
      });
    }
  }

  private calculateStrideConsistency(frameData: Array<{ frameNumber: number; timestamp: number; analysis: PoseAnalysis | null }>): number {
    const strideLengths = frameData
      .filter((frame: { frameNumber: number; timestamp: number; analysis: PoseAnalysis | null }) => frame.analysis)
      .map((frame: { frameNumber: number; timestamp: number; analysis: PoseAnalysis | null }) => frame.analysis!.metrics.strideLength);

    if (strideLengths.length < 2) return 0;

    const mean = this.average(strideLengths);
    const variance = strideLengths.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / strideLengths.length;
    const stdDev = Math.sqrt(variance);

    return stdDev / mean; // Coefficient of variation
  }

  private scoreKneeDrive(kneeAngle: number): number {
    // Optimal knee angle for running is typically 120-140 degrees
    const optimal = 130;
    const deviation = Math.abs(kneeAngle - optimal);
    return Math.max(0, 100 - (deviation * 2));
  }

  private async generateRecommendations(sessionId: string, analysisResult: any): Promise<any[]> {
    const recommendations = [];

    // Analyze posture
    if (analysisResult.averageMetrics.postureScore < 70) {
      recommendations.push({
        category: 'form',
        priority: 'high',
        title: 'Improve Running Posture',
        description: 'Your trunk angle suggests forward lean. Focus on maintaining an upright posture with slight forward lean from ankles.',
        confidence: 0.8
      });
    }

    // Analyze foot strike with proper types
    const footStrikeData = analysisResult.frameData
      .filter((frame: { frameNumber: number; timestamp: number; analysis: PoseAnalysis | null }) => frame.analysis)
      .map((frame: { frameNumber: number; timestamp: number; analysis: PoseAnalysis | null }) => frame.analysis!.metrics.footStrike);

    const heelStrikePercentage = (footStrikeData.filter((strike: string) => strike === 'heel').length / footStrikeData.length) * 100;

    if (heelStrikePercentage > 70) {
      recommendations.push({
        category: 'equipment',
        priority: 'medium',
        title: 'Consider Heel-Strike Friendly Shoes',
        description: 'Your foot strike pattern shows predominantly heel striking. Consider shoes with more heel cushioning and support.',
        confidence: 0.9
      });
    }

    // Store recommendations in database
    for (const rec of recommendations) {
      await supabase.from('pose_recommendations').insert({
        session_id: sessionId,
        ...rec,
        confidence_score: rec.confidence
      });
    }

    return recommendations;
  }

  private async updateAnalysisSession(sessionId: string, analysisResult: any): Promise<void> {
    await supabase
      .from('pose_sessions')
      .update({
        total_frames_analyzed: analysisResult.totalFrames,
        successful_detections: analysisResult.successfulDetections,
        detection_rate: analysisResult.detectionRate,
        avg_hip_angle: analysisResult.averageMetrics.hipAngle,
        avg_knee_angle: analysisResult.averageMetrics.kneeAngle,
        avg_ankle_angle: analysisResult.averageMetrics.ankleAngle,
        avg_trunk_angle: analysisResult.averageMetrics.trunkAngle,
        avg_neck_angle: analysisResult.averageMetrics.neckAngle,
        avg_stride_length: analysisResult.averageMetrics.strideLength,
        avg_posture_score: analysisResult.averageMetrics.postureScore
      })
      .eq('id', sessionId);
  }

  private async updateVideoStatus(videoId: string, status: string): Promise<void> {
    await supabase
      .from('pose_videos')
      .update({
        processing_status: status,
        processing_completed_at: new Date().toISOString()
      })
      .eq('id', videoId);
  }

  private cleanup(): void {
    if (this.video) {
      this.video.src = '';
      this.video = null;
    }
    this.canvas = null;
    this.ctx = null;
  }
}
