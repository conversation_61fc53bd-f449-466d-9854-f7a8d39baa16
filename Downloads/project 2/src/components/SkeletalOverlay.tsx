import React, { useState, useEffect } from 'react';
import { ActivityType } from '../types';

interface PoseDataPoint {
  frame_number: number;
  timestamp_seconds: number;
  pose_detected: boolean;
  detection_confidence: number;
  hip_angle: number;
  knee_angle: number;
  ankle_angle: number;
  trunk_angle: number;
  neck_angle: number;
  hip_x: number;
  hip_y: number;
  knee_x: number;
  knee_y: number;
  ankle_x: number;
  ankle_y: number;
  trunk_x: number;
  trunk_y: number;
  neck_x: number;
  neck_y: number;

  // Enhanced anatomical keypoints from BlazePose (match database schema)
  shoulder_x?: number;
  shoulder_y?: number;
  elbow_x?: number;
  elbow_y?: number;
  wrist_x?: number;
  wrist_y?: number;
  heel_x?: number;
  heel_y?: number;
  foot_x?: number;
  foot_y?: number;

  // Bilateral keypoints for comprehensive analysis
  shoulder_left_x?: number;
  shoulder_left_y?: number;
  shoulder_right_x?: number;
  shoulder_right_y?: number;
  elbow_left_x?: number;
  elbow_left_y?: number;
  elbow_right_x?: number;
  elbow_right_y?: number;
  wrist_left_x?: number;
  wrist_left_y?: number;
  wrist_right_x?: number;
  wrist_right_y?: number;
  hip_left_x?: number;
  hip_left_y?: number;
  hip_right_x?: number;
  hip_right_y?: number;
  knee_left_x?: number;
  knee_left_y?: number;
  knee_right_x?: number;
  knee_right_y?: number;
  ankle_left_x?: number;
  ankle_left_y?: number;
  ankle_right_x?: number;
  ankle_right_y?: number;

  // Enhanced confidence scores
  detection_quality?: number;
  bilateral_symmetry?: number;

  // Calculated metrics for this frame
  stride_length?: number;
  foot_strike_type?: string;
  posture_score?: number;

  // Raw keypoint data (JSON for flexibility)
  raw_keypoints?: any;
}

interface SkeletalOverlayProps {
  activity: ActivityType;
  view: 'side' | 'rear';
  currentTime: number;
  duration: number;
  videoRef: React.RefObject<HTMLVideoElement>;
  sessionId?: string;
}

interface BiomechanicalJoint {
  x: number;
  y: number;
  confidence: number;
  anatomicallyAdjusted: boolean;
  detected: boolean;
}

interface ProfessionalSkeleton {
  // Central axis
  head: BiomechanicalJoint;
  neck: BiomechanicalJoint;
  c7: BiomechanicalJoint;  // 7th cervical vertebra
  t12: BiomechanicalJoint; // 12th thoracic vertebra
  l5: BiomechanicalJoint;  // 5th lumbar vertebra
  sacrum: BiomechanicalJoint;

  // Primary leg (detected side)
  hipCenter: BiomechanicalJoint;
  hip: BiomechanicalJoint;
  knee: BiomechanicalJoint;
  ankle: BiomechanicalJoint;
  heel: BiomechanicalJoint;
  forefoot: BiomechanicalJoint;
  toes: BiomechanicalJoint;

  // Secondary leg (mirrored)
  hipMirrored: BiomechanicalJoint;
  kneeMirrored: BiomechanicalJoint;
  ankleMirrored: BiomechanicalJoint;
  heelMirrored: BiomechanicalJoint;
  forefootMirrored: BiomechanicalJoint;
  toesMirrored: BiomechanicalJoint;

  // Primary arm (detected side)
  shoulder: BiomechanicalJoint;
  elbow: BiomechanicalJoint;
  wrist: BiomechanicalJoint;
  hand: BiomechanicalJoint;

  // Secondary arm (mirrored)
  shoulderMirrored: BiomechanicalJoint;
  elbowMirrored: BiomechanicalJoint;
  wristMirrored: BiomechanicalJoint;
  handMirrored: BiomechanicalJoint;
}

interface AnatomicalAngle {
  name: string;
  value: number;
  vertex: BiomechanicalJoint;
  point1: BiomechanicalJoint;
  point2: BiomechanicalJoint;
  significance: 'primary' | 'secondary' | 'tertiary';
  confidence: number;
}

const SkeletalOverlay: React.FC<SkeletalOverlayProps> = ({
  activity,
  view,
  currentTime,
  videoRef,
  sessionId
}) => {
  const [poseData, setPoseData] = useState<PoseDataPoint[]>([]);
  const [currentPose, setCurrentPose] = useState<PoseDataPoint | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // ENHANCED BIOMECHANICAL CONSTANTS - aligned with enhanced body scale calculation
  // Based on 0-100 coordinate system with expectedHeight = 70 for full body
  const ANATOMICAL_RATIOS = {
    // HEAD AND NECK (based on 70-unit body height)
    headToNeck: 5.6,        // 8% of 70 = 5.6 units
    neckToShoulder: 8.4,    // 12% of 70 = 8.4 units

    // ARM SEGMENTS (based on 70-unit body height)
    shoulderToElbow: 12.6,  // 18% of 70 = 12.6 units
    elbowToWrist: 10.5,     // 15% of 70 = 10.5 units
    wristToHand: 5.6,       // 8% of 70 = 5.6 units

    // SPINE SEGMENTS (based on 70-unit body height)
    spineC7ToT12: 10.5,     // 15% of 70 = 10.5 units
    spineT12ToL5: 7.0,      // 10% of 70 = 7.0 units
    spineL5ToSacrum: 3.5,   // 5% of 70 = 3.5 units

    // LEG SEGMENTS (aligned with enhanced body scale measurements)
    hipToKnee: 22.5,        // 45 units (leg height) * 50% = 22.5 units
    kneeToAnkle: 22.5,      // 45 units (leg height) * 50% = 22.5 units (matches 23-unit shin height)

    // FOOT SEGMENTS (based on 70-unit body height)
    ankleToHeel: 3.5,       // 5% of 70 = 3.5 units
    heelToForefoot: 8.4,    // 12% of 70 = 8.4 units
    forefootToToes: 5.6,    // 8% of 70 = 5.6 units

    // BODY WIDTH (based on 70-unit body height)
    shoulderWidth: 15.4,    // 22% of 70 = 15.4 units
    hipWidth: 12.6          // 18% of 70 = 12.6 units
  };

  // Load pose data with optimized binary search
  useEffect(() => {
    if (sessionId) {
      loadPoseData();
    }
  }, [sessionId]);

  useEffect(() => {
    if (poseData.length > 0) {
      const closestPose = findClosestPoseOptimized(poseData, currentTime);
      if (!currentPose || closestPose.frame_number !== currentPose.frame_number) {
        setCurrentPose(closestPose);
      }
    }
  }, [currentTime, poseData, currentPose]);

  const findClosestPoseOptimized = (data: PoseDataPoint[], targetTime: number): PoseDataPoint => {
    if (data.length === 0) return data[0];
    if (data.length === 1) return data[0];

    let left = 0;
    let right = data.length - 1;
    let closest = data[0];
    let minDiff = Math.abs(data[0].timestamp_seconds - targetTime);

    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      const diff = Math.abs(data[mid].timestamp_seconds - targetTime);

      if (diff < minDiff) {
        minDiff = diff;
        closest = data[mid];
      }

      if (data[mid].timestamp_seconds < targetTime) {
        left = mid + 1;
      } else {
        right = mid - 1;
      }
    }

    return closest;
  };

  const loadPoseData = async () => {
    if (!sessionId) return;

    setIsLoading(true);
    try {
      const { default: supabase } = await import('../utils/supabaseClient');

      const { data, error } = await supabase
        .from('pose_data')
        .select('*')
        .eq('session_id', sessionId)
        .order('frame_number');

      if (error) {
        setError(`Failed to load pose data: ${error.message}`);
        return;
      }

      if (!data || data.length === 0) {
        setError('No pose data found for this session');
        setPoseData([]);
      } else {
        setPoseData(data);
        setError(null);
      }
    } catch (err) {
      setError(`Failed to load pose data: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // BIOMECHANICAL JOINT POSITIONING with anatomical constraints
  const applyAnatomicalConstraints = (rawPose: PoseDataPoint): ProfessionalSkeleton => {
    // COORDINATE PASS-THROUGH DEBUG: Track coordinate transformations through processing pipeline
    console.log('🔍 SkeletalOverlay - Raw Database Input Coordinates (applyAnatomicalConstraints entry):', {
      frame_number: rawPose.frame_number,
      raw_database_coordinates: {
        hip: { x: rawPose.hip_x, y: rawPose.hip_y },
        knee: { x: rawPose.knee_x, y: rawPose.knee_y },
        ankle: { x: rawPose.ankle_x, y: rawPose.ankle_y },
        trunk: { x: rawPose.trunk_x, y: rawPose.trunk_y },
        neck: { x: rawPose.neck_x, y: rawPose.neck_y }
      },
      coordinate_validation: {
        hip_defined: rawPose.hip_x !== undefined && rawPose.hip_x !== null,
        knee_defined: rawPose.knee_x !== undefined && rawPose.knee_x !== null,
        ankle_defined: rawPose.ankle_x !== undefined && rawPose.ankle_x !== null,
        coordinate_range_check: {
          hip_in_0_100: rawPose.hip_x >= 0 && rawPose.hip_x <= 100,
          knee_in_0_100: rawPose.knee_x >= 0 && rawPose.knee_x <= 100,
          ankle_in_0_100: rawPose.ankle_x >= 0 && rawPose.ankle_x <= 100
        }
      },
      processing_stage: 'ENTRY_TO_applyAnatomicalConstraints'
    });

    const baseConfidence = rawPose.detection_confidence || 0.8;

    // Determine which side is primary based on detection confidence
    const leftConfidence = (rawPose.shoulder_left_x ? 1 : 0) + (rawPose.elbow_left_x ? 1 : 0) + (rawPose.wrist_left_x ? 1 : 0);
    const rightConfidence = (rawPose.shoulder_right_x ? 1 : 0) + (rawPose.elbow_right_x ? 1 : 0) + (rawPose.wrist_right_x ? 1 : 0);
    const usePrimarySide = leftConfidence >= rightConfidence ? 'left' : 'right';

    // Debug: Log bilateral detection status and primary side selection
    const detectedKeypoints = {
      primarySide: usePrimarySide,
      leftSideDetections: leftConfidence,
      rightSideDetections: rightConfidence,
      shoulder: {
        general: rawPose.shoulder_x !== undefined && rawPose.shoulder_x !== null,
        left: rawPose.shoulder_left_x !== undefined && rawPose.shoulder_left_x !== null,
        right: rawPose.shoulder_right_x !== undefined && rawPose.shoulder_right_x !== null
      },
      elbow: {
        general: rawPose.elbow_x !== undefined && rawPose.elbow_x !== null,
        left: rawPose.elbow_left_x !== undefined && rawPose.elbow_left_x !== null,
        right: rawPose.elbow_right_x !== undefined && rawPose.elbow_right_x !== null
      },
      wrist: {
        general: rawPose.wrist_x !== undefined && rawPose.wrist_x !== null,
        left: rawPose.wrist_left_x !== undefined && rawPose.wrist_left_x !== null,
        right: rawPose.wrist_right_x !== undefined && rawPose.wrist_right_x !== null
      },
      legs: {
        hip_left: rawPose.hip_left_x !== undefined && rawPose.hip_left_x !== null,
        hip_right: rawPose.hip_right_x !== undefined && rawPose.hip_right_x !== null,
        knee_left: rawPose.knee_left_x !== undefined && rawPose.knee_left_x !== null,
        knee_right: rawPose.knee_right_x !== undefined && rawPose.knee_right_x !== null,
        ankle_left: rawPose.ankle_left_x !== undefined && rawPose.ankle_left_x !== null,
        ankle_right: rawPose.ankle_right_x !== undefined && rawPose.ankle_right_x !== null
      },
      heel: !!(rawPose.heel_x !== undefined && rawPose.heel_x !== null),
      foot: !!(rawPose.foot_x !== undefined && rawPose.foot_x !== null)
    };

    if (rawPose.frame_number % 30 === 0) { // Log every 30th frame to avoid spam
      console.log(`Frame ${rawPose.frame_number} - Primary side detection:`, {
        primarySide: usePrimarySide,
        leftConfidence: leftConfidence,
        rightConfidence: rightConfidence,
        detectionStatus: detectedKeypoints
      });
      console.log(`Frame ${rawPose.frame_number} - Bilateral keypoint values:`, {
        shoulder: {
          general: { x: rawPose.shoulder_x, y: rawPose.shoulder_y },
          left: { x: rawPose.shoulder_left_x, y: rawPose.shoulder_left_y },
          right: { x: rawPose.shoulder_right_x, y: rawPose.shoulder_right_y }
        },
        elbow: {
          general: { x: rawPose.elbow_x, y: rawPose.elbow_y },
          left: { x: rawPose.elbow_left_x, y: rawPose.elbow_left_y },
          right: { x: rawPose.elbow_right_x, y: rawPose.elbow_right_y }
        },
        wrist: {
          general: { x: rawPose.wrist_x, y: rawPose.wrist_y },
          left: { x: rawPose.wrist_left_x, y: rawPose.wrist_left_y },
          right: { x: rawPose.wrist_right_x, y: rawPose.wrist_right_y }
        },
        legs: {
          hip: { left: { x: rawPose.hip_left_x, y: rawPose.hip_left_y }, right: { x: rawPose.hip_right_x, y: rawPose.hip_right_y } },
          knee: { left: { x: rawPose.knee_left_x, y: rawPose.knee_left_y }, right: { x: rawPose.knee_right_x, y: rawPose.knee_right_y } },
          ankle: { left: { x: rawPose.ankle_left_x, y: rawPose.ankle_left_y }, right: { x: rawPose.ankle_right_x, y: rawPose.ankle_right_y } }
        }
      });
    }

    // Enhanced body scale calculation with proportional analysis
    const detectedNeckY = rawPose.neck_y;
    const detectedAnkleY = rawPose.ankle_y;

    let bodyScale: number;
    let scaleSource: string;

    // Primary method: Use neck-to-ankle measurement if both keypoints are detected
    if (detectedNeckY !== undefined && detectedAnkleY !== undefined) {
      const detectedHeight = Math.abs(detectedNeckY - detectedAnkleY);
      const expectedHeight = 70; // More realistic proportion for 0-100 coordinate system
      const calculatedScale = detectedHeight / expectedHeight;

      // Apply safety bounds to prevent extreme distortion
      bodyScale = Math.max(0.7, Math.min(1.3, calculatedScale));
      scaleSource = `neck-ankle (${detectedHeight.toFixed(1)}px, scale: ${calculatedScale.toFixed(2)} → ${bodyScale.toFixed(2)})`;

      // Additional validation for reasonable body proportions
      if (detectedHeight < 30 || detectedHeight > 95) {
        console.warn(`Frame ${rawPose.frame_number}: Unusual body height detected (${detectedHeight.toFixed(1)}), using constrained scale`);
      }
    }
    // Alternative method: Use hip-to-ankle measurement (more reliable for running analysis)
    else if (rawPose.hip_y !== undefined && detectedAnkleY !== undefined) {
      const hipToAnkleHeight = Math.abs(rawPose.hip_y - detectedAnkleY);
      const expectedLegHeight = 45; // Expected leg proportion in 0-100 coordinate system
      const calculatedScale = hipToAnkleHeight / expectedLegHeight;

      bodyScale = Math.max(0.7, Math.min(1.3, calculatedScale));
      scaleSource = `hip-ankle (${hipToAnkleHeight.toFixed(1)}px, scale: ${calculatedScale.toFixed(2)} → ${bodyScale.toFixed(2)})`;
    }
    // Tertiary method: Use knee-to-ankle measurement as last resort
    else if (rawPose.knee_y !== undefined && detectedAnkleY !== undefined) {
      const kneeToAnkleHeight = Math.abs(rawPose.knee_y - detectedAnkleY);
      const expectedShinHeight = 23; // Expected shin proportion in 0-100 coordinate system
      const calculatedScale = kneeToAnkleHeight / expectedShinHeight;

      bodyScale = Math.max(0.7, Math.min(1.3, calculatedScale));
      scaleSource = `knee-ankle (${kneeToAnkleHeight.toFixed(1)}px, scale: ${calculatedScale.toFixed(2)} → ${bodyScale.toFixed(2)})`;
    }
    // Final fallback: Use neutral scale when no reliable measurements are available
    else {
      bodyScale = 1.0;
      scaleSource = `neutral fallback (insufficient keypoints for measurement)`;
    }

    // Validate bodyScale is within reasonable bounds
    if (!isFinite(bodyScale) || bodyScale <= 0) {
      console.error(`Frame ${rawPose.frame_number}: Invalid body scale calculated (${bodyScale}), using neutral scale`);
      bodyScale = 1.0;
      scaleSource = `error recovery (invalid scale)`;
    }

    // Debug logging for body scale calculation
    if (rawPose.frame_number % 30 === 0) {
      console.log(`Frame ${rawPose.frame_number} - Body scale: ${bodyScale.toFixed(2)} (${scaleSource})`);
      console.log(`Frame ${rawPose.frame_number} - Available keypoints for scaling:`, {
        neck_y: detectedNeckY,
        ankle_y: detectedAnkleY,
        hip_y: rawPose.hip_y,
        knee_y: rawPose.knee_y
      });
    }

    // Primary detected joints with confidence weighting
    const rawHip = { x: rawPose.hip_x || 50, y: rawPose.hip_y || 50 };
    const rawKnee = { x: rawPose.knee_x || 50, y: rawPose.knee_y || 70 };
    const rawAnkle = { x: rawPose.ankle_x || 50, y: rawPose.ankle_y || 90 };
    const rawTrunk = { x: rawPose.trunk_x || 50, y: rawPose.trunk_y || 30 };
    const rawNeck = { x: rawPose.neck_x || 50, y: rawPose.neck_y || 15 };

    // CRITICAL DEBUG: Log actual coordinate values from database
    if (rawPose.frame_number % 30 === 0) { // Log every 30th frame to avoid spam
      console.log(`🔍 SkeletalOverlay Frame ${rawPose.frame_number} - Raw Database Coordinates:`, {
        hip: { x: rawPose.hip_x, y: rawPose.hip_y },
        knee: { x: rawPose.knee_x, y: rawPose.knee_y },
        ankle: { x: rawPose.ankle_x, y: rawPose.ankle_y },
        trunk: { x: rawPose.trunk_x, y: rawPose.trunk_y },
        neck: { x: rawPose.neck_x, y: rawPose.neck_y },
        coordinate_range_analysis: {
          hip_x_range: rawPose.hip_x ? (rawPose.hip_x > 1 ? 'PIXEL_LIKE' : 'NORMALIZED_LIKE') : 'MISSING',
          hip_y_range: rawPose.hip_y ? (rawPose.hip_y > 1 ? 'PIXEL_LIKE' : 'NORMALIZED_LIKE') : 'MISSING',
          expected_range: '0-100 for SVG viewBox'
        }
      });
    }

    // ANATOMICALLY ACCURATE SPINE CONSTRUCTION
    const spineBase = { x: rawTrunk.x, y: rawHip.y - (ANATOMICAL_RATIOS.spineL5ToSacrum * bodyScale) };

    const skeleton: ProfessionalSkeleton = {
      // CENTRAL AXIS - anatomically accurate spine
      head: {
        x: rawNeck.x,
        y: rawNeck.y - (ANATOMICAL_RATIOS.headToNeck * bodyScale),
        confidence: baseConfidence * 0.9,
        anatomicallyAdjusted: true,
        detected: false
      },
      neck: {
        x: rawNeck.x,
        y: rawNeck.y,
        confidence: baseConfidence,
        anatomicallyAdjusted: false,
        detected: true
      },
      c7: {
        x: rawNeck.x,
        y: rawNeck.y + (ANATOMICAL_RATIOS.neckToShoulder * bodyScale),
        confidence: baseConfidence * 0.95,
        anatomicallyAdjusted: true,
        detected: false
      },
      t12: {
        x: rawTrunk.x,
        y: rawTrunk.y + (ANATOMICAL_RATIOS.spineC7ToT12 * bodyScale),
        confidence: baseConfidence * 0.9,
        anatomicallyAdjusted: true,
        detected: false
      },
      l5: {
        x: spineBase.x,
        y: spineBase.y,
        confidence: baseConfidence * 0.85,
        anatomicallyAdjusted: true,
        detected: false
      },
      sacrum: {
        x: spineBase.x,
        y: rawHip.y,
        confidence: baseConfidence * 0.8,
        anatomicallyAdjusted: true,
        detected: false
      },

      // HIP CENTER (anatomical center of pelvis)
      hipCenter: {
        x: rawTrunk.x,
        y: rawHip.y,
        confidence: baseConfidence,
        anatomicallyAdjusted: true,
        detected: false
      },

      // PRIMARY LEG - USE PRIMARY SIDE DETECTION WITH BILATERAL FALLBACK
      hip: {
        x: rawPose.hip_x ??
           (usePrimarySide === 'left' ? rawPose.hip_left_x : rawPose.hip_right_x) ??
           (usePrimarySide === 'left' ? rawPose.hip_right_x : rawPose.hip_left_x) ??
           (rawTrunk.x + (ANATOMICAL_RATIOS.hipWidth * bodyScale * 0.5 * (rawHip.x > rawTrunk.x ? 1 : -1))),
        y: rawPose.hip_y ??
           (usePrimarySide === 'left' ? rawPose.hip_left_y : rawPose.hip_right_y) ??
           (usePrimarySide === 'left' ? rawPose.hip_right_y : rawPose.hip_left_y) ??
           (rawTrunk.y + (ANATOMICAL_RATIOS.spineL5ToSacrum * bodyScale)),
        confidence: (rawPose.hip_x ??
                    (usePrimarySide === 'left' ? rawPose.hip_left_x : rawPose.hip_right_x) ??
                    (usePrimarySide === 'left' ? rawPose.hip_right_x : rawPose.hip_left_x)) !== undefined ?
                   baseConfidence : baseConfidence * 0.8,
        anatomicallyAdjusted: (rawPose.hip_x ??
                              (usePrimarySide === 'left' ? rawPose.hip_left_x : rawPose.hip_right_x) ??
                              (usePrimarySide === 'left' ? rawPose.hip_right_x : rawPose.hip_left_x)) === undefined,
        detected: (rawPose.hip_x ??
                  (usePrimarySide === 'left' ? rawPose.hip_left_x : rawPose.hip_right_x) ??
                  (usePrimarySide === 'left' ? rawPose.hip_right_x : rawPose.hip_left_x)) !== undefined
      },
      knee: {
        x: rawPose.knee_x ??
           (usePrimarySide === 'left' ? rawPose.knee_left_x : rawPose.knee_right_x) ??
           (usePrimarySide === 'left' ? rawPose.knee_right_x : rawPose.knee_left_x) ??
           (rawTrunk.x + (ANATOMICAL_RATIOS.hipWidth * bodyScale * 0.6 * (rawHip.x > rawTrunk.x ? 1 : -1))),
        y: rawPose.knee_y ??
           (usePrimarySide === 'left' ? rawPose.knee_left_y : rawPose.knee_right_y) ??
           (usePrimarySide === 'left' ? rawPose.knee_right_y : rawPose.knee_left_y) ??
           (rawTrunk.y + (ANATOMICAL_RATIOS.spineL5ToSacrum + ANATOMICAL_RATIOS.hipToKnee) * bodyScale),
        confidence: (rawPose.knee_x ??
                    (usePrimarySide === 'left' ? rawPose.knee_left_x : rawPose.knee_right_x) ??
                    (usePrimarySide === 'left' ? rawPose.knee_right_x : rawPose.knee_left_x)) !== undefined ?
                   baseConfidence : baseConfidence * 0.8,
        anatomicallyAdjusted: (rawPose.knee_x ??
                              (usePrimarySide === 'left' ? rawPose.knee_left_x : rawPose.knee_right_x) ??
                              (usePrimarySide === 'left' ? rawPose.knee_right_x : rawPose.knee_left_x)) === undefined,
        detected: (rawPose.knee_x ??
                  (usePrimarySide === 'left' ? rawPose.knee_left_x : rawPose.knee_right_x) ??
                  (usePrimarySide === 'left' ? rawPose.knee_right_x : rawPose.knee_left_x)) !== undefined
      },
      ankle: {
        x: rawPose.ankle_x ??
           (usePrimarySide === 'left' ? rawPose.ankle_left_x : rawPose.ankle_right_x) ??
           (usePrimarySide === 'left' ? rawPose.ankle_right_x : rawPose.ankle_left_x) ??
           (rawTrunk.x + (ANATOMICAL_RATIOS.hipWidth * bodyScale * 0.7 * (rawHip.x > rawTrunk.x ? 1 : -1))),
        y: rawPose.ankle_y ??
           (usePrimarySide === 'left' ? rawPose.ankle_left_y : rawPose.ankle_right_y) ??
           (usePrimarySide === 'left' ? rawPose.ankle_right_y : rawPose.ankle_left_y) ??
           (rawTrunk.y + (ANATOMICAL_RATIOS.spineL5ToSacrum + ANATOMICAL_RATIOS.hipToKnee + ANATOMICAL_RATIOS.kneeToAnkle) * bodyScale),
        confidence: (rawPose.ankle_x ??
                    (usePrimarySide === 'left' ? rawPose.ankle_left_x : rawPose.ankle_right_x) ??
                    (usePrimarySide === 'left' ? rawPose.ankle_right_x : rawPose.ankle_left_x)) !== undefined ?
                   baseConfidence : baseConfidence * 0.8,
        anatomicallyAdjusted: (rawPose.ankle_x ??
                              (usePrimarySide === 'left' ? rawPose.ankle_left_x : rawPose.ankle_right_x) ??
                              (usePrimarySide === 'left' ? rawPose.ankle_right_x : rawPose.ankle_left_x)) === undefined,
        detected: (rawPose.ankle_x ??
                  (usePrimarySide === 'left' ? rawPose.ankle_left_x : rawPose.ankle_right_x) ??
                  (usePrimarySide === 'left' ? rawPose.ankle_right_x : rawPose.ankle_left_x)) !== undefined
      },
      heel: {
        x: rawPose.heel_x !== undefined && rawPose.heel_x !== null ? rawPose.heel_x :
           ((rawPose.ankle_x ??
             (usePrimarySide === 'left' ? rawPose.ankle_left_x : rawPose.ankle_right_x) ??
             (usePrimarySide === 'left' ? rawPose.ankle_right_x : rawPose.ankle_left_x) ??
             rawAnkle.x) - (ANATOMICAL_RATIOS.ankleToHeel * bodyScale)),
        y: rawPose.heel_y !== undefined && rawPose.heel_y !== null ? rawPose.heel_y :
           ((rawPose.ankle_y ??
             (usePrimarySide === 'left' ? rawPose.ankle_left_y : rawPose.ankle_right_y) ??
             (usePrimarySide === 'left' ? rawPose.ankle_right_y : rawPose.ankle_left_y) ??
             rawAnkle.y) + (ANATOMICAL_RATIOS.ankleToHeel * bodyScale * 0.5)), // Slight downward offset
        confidence: (rawPose.heel_x !== undefined && rawPose.heel_x !== null) ? baseConfidence * 0.9 : baseConfidence * 0.8,
        anatomicallyAdjusted: !(rawPose.heel_x !== undefined && rawPose.heel_x !== null),
        detected: !!(rawPose.heel_x !== undefined && rawPose.heel_x !== null)
      },
      forefoot: {
        x: rawPose.foot_x !== undefined && rawPose.foot_x !== null ? rawPose.foot_x :
           ((rawPose.ankle_x ??
             (usePrimarySide === 'left' ? rawPose.ankle_left_x : rawPose.ankle_right_x) ??
             (usePrimarySide === 'left' ? rawPose.ankle_right_x : rawPose.ankle_left_x) ??
             rawAnkle.x) + (ANATOMICAL_RATIOS.heelToForefoot * bodyScale)),
        y: rawPose.foot_y !== undefined && rawPose.foot_y !== null ? rawPose.foot_y :
           ((rawPose.ankle_y ??
             (usePrimarySide === 'left' ? rawPose.ankle_left_y : rawPose.ankle_right_y) ??
             (usePrimarySide === 'left' ? rawPose.ankle_right_y : rawPose.ankle_left_y) ??
             rawAnkle.y) + (ANATOMICAL_RATIOS.heelToForefoot * bodyScale * 0.3)), // Slight downward offset
        confidence: (rawPose.foot_x !== undefined && rawPose.foot_x !== null) ? baseConfidence * 0.9 : baseConfidence * 0.7,
        anatomicallyAdjusted: !(rawPose.foot_x !== undefined && rawPose.foot_x !== null),
        detected: !!(rawPose.foot_x !== undefined && rawPose.foot_x !== null)
      },
      toes: {
        x: rawPose.foot_x !== undefined && rawPose.foot_x !== null ?
           rawPose.foot_x + (ANATOMICAL_RATIOS.forefootToToes * bodyScale) :
           ((rawPose.ankle_x ??
             (usePrimarySide === 'left' ? rawPose.ankle_left_x : rawPose.ankle_right_x) ??
             (usePrimarySide === 'left' ? rawPose.ankle_right_x : rawPose.ankle_left_x) ??
             rawAnkle.x) + ((ANATOMICAL_RATIOS.heelToForefoot + ANATOMICAL_RATIOS.forefootToToes) * bodyScale)),
        y: rawPose.foot_y !== undefined && rawPose.foot_y !== null ?
           rawPose.foot_y + (ANATOMICAL_RATIOS.forefootToToes * bodyScale * 0.2) :
           ((rawPose.ankle_y ??
             (usePrimarySide === 'left' ? rawPose.ankle_left_y : rawPose.ankle_right_y) ??
             (usePrimarySide === 'left' ? rawPose.ankle_right_y : rawPose.ankle_left_y) ??
             rawAnkle.y) + (ANATOMICAL_RATIOS.forefootToToes * bodyScale * 0.2)),
        confidence: (rawPose.foot_x !== undefined && rawPose.foot_x !== null) ? baseConfidence * 0.8 : baseConfidence * 0.6,
        anatomicallyAdjusted: !(rawPose.foot_x !== undefined && rawPose.foot_x !== null),
        detected: !!(rawPose.foot_x !== undefined && rawPose.foot_x !== null)
      },

      // BILATERAL SYMMETRY with anatomical spacing
      hipMirrored: {
        x: rawTrunk.x - (rawHip.x - rawTrunk.x),
        y: rawHip.y,
        confidence: baseConfidence * 0.75,
        anatomicallyAdjusted: true,
        detected: false
      },
      kneeMirrored: {
        x: rawTrunk.x - (rawKnee.x - rawTrunk.x),
        y: rawKnee.y,
        confidence: baseConfidence * 0.75,
        anatomicallyAdjusted: true,
        detected: false
      },
      ankleMirrored: {
        x: rawTrunk.x - (rawAnkle.x - rawTrunk.x),
        y: rawAnkle.y,
        confidence: baseConfidence * 0.75,
        anatomicallyAdjusted: true,
        detected: false
      },
      heelMirrored: {
        x: rawTrunk.x - (rawAnkle.x - rawTrunk.x) - (ANATOMICAL_RATIOS.ankleToHeel * bodyScale),
        y: rawAnkle.y + (ANATOMICAL_RATIOS.ankleToHeel * bodyScale * 0.5),
        confidence: baseConfidence * 0.6,
        anatomicallyAdjusted: true,
        detected: false
      },
      forefootMirrored: {
        x: rawTrunk.x - (rawAnkle.x - rawTrunk.x) + (ANATOMICAL_RATIOS.heelToForefoot * bodyScale),
        y: rawAnkle.y + (ANATOMICAL_RATIOS.heelToForefoot * bodyScale * 0.3),
        confidence: baseConfidence * 0.5,
        anatomicallyAdjusted: true,
        detected: false
      },
      toesMirrored: {
        x: rawTrunk.x - (rawAnkle.x - rawTrunk.x) + ((ANATOMICAL_RATIOS.heelToForefoot + ANATOMICAL_RATIOS.forefootToToes) * bodyScale),
        y: rawAnkle.y + (ANATOMICAL_RATIOS.forefootToToes * bodyScale * 0.2),
        confidence: baseConfidence * 0.4,
        anatomicallyAdjusted: true,
        detected: false
      },

      // PRIMARY ARM - USE PRIMARY SIDE DETECTION WITH BILATERAL FALLBACK
      shoulder: {
        x: rawPose.shoulder_x ??
           (usePrimarySide === 'left' ? rawPose.shoulder_left_x : rawPose.shoulder_right_x) ??
           (usePrimarySide === 'left' ? rawPose.shoulder_right_x : rawPose.shoulder_left_x) ??
           (rawTrunk.x + (ANATOMICAL_RATIOS.shoulderWidth * bodyScale * 0.4 * (rawHip.x > rawTrunk.x ? 1 : -1))),
        y: rawPose.shoulder_y ??
           (usePrimarySide === 'left' ? rawPose.shoulder_left_y : rawPose.shoulder_right_y) ??
           (usePrimarySide === 'left' ? rawPose.shoulder_right_y : rawPose.shoulder_left_y) ??
           (rawNeck.y + (ANATOMICAL_RATIOS.neckToShoulder * bodyScale)),
        confidence: (rawPose.shoulder_x ??
                    (usePrimarySide === 'left' ? rawPose.shoulder_left_x : rawPose.shoulder_right_x) ??
                    (usePrimarySide === 'left' ? rawPose.shoulder_right_x : rawPose.shoulder_left_x)) !== undefined ?
                   baseConfidence : baseConfidence * 0.7,
        anatomicallyAdjusted: (rawPose.shoulder_x ??
                              (usePrimarySide === 'left' ? rawPose.shoulder_left_x : rawPose.shoulder_right_x) ??
                              (usePrimarySide === 'left' ? rawPose.shoulder_right_x : rawPose.shoulder_left_x)) === undefined,
        detected: (rawPose.shoulder_x ??
                  (usePrimarySide === 'left' ? rawPose.shoulder_left_x : rawPose.shoulder_right_x) ??
                  (usePrimarySide === 'left' ? rawPose.shoulder_right_x : rawPose.shoulder_left_x)) !== undefined
      },
      elbow: {
        x: rawPose.elbow_x ??
           (usePrimarySide === 'left' ? rawPose.elbow_left_x : rawPose.elbow_right_x) ??
           (usePrimarySide === 'left' ? rawPose.elbow_right_x : rawPose.elbow_left_x) ??
           (rawTrunk.x + (ANATOMICAL_RATIOS.shoulderWidth * bodyScale * 0.5 * (rawHip.x > rawTrunk.x ? 1 : -1))),
        y: rawPose.elbow_y ??
           (usePrimarySide === 'left' ? rawPose.elbow_left_y : rawPose.elbow_right_y) ??
           (usePrimarySide === 'left' ? rawPose.elbow_right_y : rawPose.elbow_left_y) ??
           (rawNeck.y + ((ANATOMICAL_RATIOS.neckToShoulder + ANATOMICAL_RATIOS.shoulderToElbow) * bodyScale)),
        confidence: (rawPose.elbow_x ??
                    (usePrimarySide === 'left' ? rawPose.elbow_left_x : rawPose.elbow_right_x) ??
                    (usePrimarySide === 'left' ? rawPose.elbow_right_x : rawPose.elbow_left_x)) !== undefined ?
                   baseConfidence : baseConfidence * 0.6,
        anatomicallyAdjusted: (rawPose.elbow_x ??
                              (usePrimarySide === 'left' ? rawPose.elbow_left_x : rawPose.elbow_right_x) ??
                              (usePrimarySide === 'left' ? rawPose.elbow_right_x : rawPose.elbow_left_x)) === undefined,
        detected: (rawPose.elbow_x ??
                  (usePrimarySide === 'left' ? rawPose.elbow_left_x : rawPose.elbow_right_x) ??
                  (usePrimarySide === 'left' ? rawPose.elbow_right_x : rawPose.elbow_left_x)) !== undefined
      },
      wrist: {
        x: rawPose.wrist_x ??
           (usePrimarySide === 'left' ? rawPose.wrist_left_x : rawPose.wrist_right_x) ??
           (usePrimarySide === 'left' ? rawPose.wrist_right_x : rawPose.wrist_left_x) ??
           (rawTrunk.x + (ANATOMICAL_RATIOS.shoulderWidth * bodyScale * 0.6 * (rawHip.x > rawTrunk.x ? 1 : -1))),
        y: rawPose.wrist_y ??
           (usePrimarySide === 'left' ? rawPose.wrist_left_y : rawPose.wrist_right_y) ??
           (usePrimarySide === 'left' ? rawPose.wrist_right_y : rawPose.wrist_left_y) ??
           (rawNeck.y + ((ANATOMICAL_RATIOS.neckToShoulder + ANATOMICAL_RATIOS.shoulderToElbow + ANATOMICAL_RATIOS.elbowToWrist) * bodyScale)),
        confidence: (rawPose.wrist_x ??
                    (usePrimarySide === 'left' ? rawPose.wrist_left_x : rawPose.wrist_right_x) ??
                    (usePrimarySide === 'left' ? rawPose.wrist_right_x : rawPose.wrist_left_x)) !== undefined ?
                   baseConfidence : baseConfidence * 0.5,
        anatomicallyAdjusted: (rawPose.wrist_x ??
                              (usePrimarySide === 'left' ? rawPose.wrist_left_x : rawPose.wrist_right_x) ??
                              (usePrimarySide === 'left' ? rawPose.wrist_right_x : rawPose.wrist_left_x)) === undefined,
        detected: (rawPose.wrist_x ??
                  (usePrimarySide === 'left' ? rawPose.wrist_left_x : rawPose.wrist_right_x) ??
                  (usePrimarySide === 'left' ? rawPose.wrist_right_x : rawPose.wrist_left_x)) !== undefined
      },
      hand: {
        x: (rawPose.wrist_x ??
            (usePrimarySide === 'left' ? rawPose.wrist_left_x : rawPose.wrist_right_x) ??
            (usePrimarySide === 'left' ? rawPose.wrist_right_x : rawPose.wrist_left_x)) !== undefined ?
           (rawPose.wrist_x ??
            (usePrimarySide === 'left' ? rawPose.wrist_left_x : rawPose.wrist_right_x) ??
            (usePrimarySide === 'left' ? rawPose.wrist_right_x : rawPose.wrist_left_x))! +
           (ANATOMICAL_RATIOS.wristToHand * bodyScale *
            ((rawPose.wrist_x ??
              (usePrimarySide === 'left' ? rawPose.wrist_left_x : rawPose.wrist_right_x) ??
              (usePrimarySide === 'left' ? rawPose.wrist_right_x : rawPose.wrist_left_x))! > rawTrunk.x ? 1 : -1)) :
           (rawTrunk.x + (ANATOMICAL_RATIOS.shoulderWidth * bodyScale * 0.7 * (rawHip.x > rawTrunk.x ? 1 : -1))),
        y: (rawPose.wrist_y ??
            (usePrimarySide === 'left' ? rawPose.wrist_left_y : rawPose.wrist_right_y) ??
            (usePrimarySide === 'left' ? rawPose.wrist_right_y : rawPose.wrist_left_y)) !== undefined ?
           (rawPose.wrist_y ??
            (usePrimarySide === 'left' ? rawPose.wrist_left_y : rawPose.wrist_right_y) ??
            (usePrimarySide === 'left' ? rawPose.wrist_right_y : rawPose.wrist_left_y))! +
           (ANATOMICAL_RATIOS.wristToHand * bodyScale * 0.3) :
           (rawNeck.y + ((ANATOMICAL_RATIOS.neckToShoulder + ANATOMICAL_RATIOS.shoulderToElbow + ANATOMICAL_RATIOS.elbowToWrist + ANATOMICAL_RATIOS.wristToHand) * bodyScale)),
        confidence: (rawPose.wrist_x ??
                    (usePrimarySide === 'left' ? rawPose.wrist_left_x : rawPose.wrist_right_x) ??
                    (usePrimarySide === 'left' ? rawPose.wrist_right_x : rawPose.wrist_left_x)) !== undefined ?
                   baseConfidence * 0.8 : baseConfidence * 0.4,
        anatomicallyAdjusted: (rawPose.wrist_x ??
                              (usePrimarySide === 'left' ? rawPose.wrist_left_x : rawPose.wrist_right_x) ??
                              (usePrimarySide === 'left' ? rawPose.wrist_right_x : rawPose.wrist_left_x)) === undefined,
        detected: (rawPose.wrist_x ??
                  (usePrimarySide === 'left' ? rawPose.wrist_left_x : rawPose.wrist_right_x) ??
                  (usePrimarySide === 'left' ? rawPose.wrist_right_x : rawPose.wrist_left_x)) !== undefined
      },

      // SECONDARY ARM - mirrored with anatomical constraints
      shoulderMirrored: {
        x: rawTrunk.x - (ANATOMICAL_RATIOS.shoulderWidth * bodyScale * 0.4 * (rawHip.x > rawTrunk.x ? 1 : -1)),
        y: rawNeck.y + (ANATOMICAL_RATIOS.neckToShoulder * bodyScale),
        confidence: baseConfidence * 0.6,
        anatomicallyAdjusted: true,
        detected: false
      },
      elbowMirrored: {
        x: rawTrunk.x - (ANATOMICAL_RATIOS.shoulderWidth * bodyScale * 0.5 * (rawHip.x > rawTrunk.x ? 1 : -1)),
        y: rawNeck.y + ((ANATOMICAL_RATIOS.neckToShoulder + ANATOMICAL_RATIOS.shoulderToElbow) * bodyScale),
        confidence: baseConfidence * 0.5,
        anatomicallyAdjusted: true,
        detected: false
      },
      wristMirrored: {
        x: rawTrunk.x - (ANATOMICAL_RATIOS.shoulderWidth * bodyScale * 0.6 * (rawHip.x > rawTrunk.x ? 1 : -1)),
        y: rawNeck.y + ((ANATOMICAL_RATIOS.neckToShoulder + ANATOMICAL_RATIOS.shoulderToElbow + ANATOMICAL_RATIOS.elbowToWrist) * bodyScale),
        confidence: baseConfidence * 0.4,
        anatomicallyAdjusted: true,
        detected: false
      },
      handMirrored: {
        x: rawTrunk.x - (ANATOMICAL_RATIOS.shoulderWidth * bodyScale * 0.7 * (rawHip.x > rawTrunk.x ? 1 : -1)),
        y: rawNeck.y + ((ANATOMICAL_RATIOS.neckToShoulder + ANATOMICAL_RATIOS.shoulderToElbow + ANATOMICAL_RATIOS.elbowToWrist + ANATOMICAL_RATIOS.wristToHand) * bodyScale),
        confidence: baseConfidence * 0.3,
        anatomicallyAdjusted: true,
        detected: false
      }
    };

    return skeleton;
  };

  // PRECISE INTERNAL ANGLE CALCULATION with biomechanical validation
  const calculatePreciseInternalAngle = (point1: BiomechanicalJoint, vertex: BiomechanicalJoint, point2: BiomechanicalJoint): number => {
    const vector1 = {
      x: point1.x - vertex.x,
      y: point1.y - vertex.y
    };

    const vector2 = {
      x: point2.x - vertex.x,
      y: point2.y - vertex.y
    };

    const dot = vector1.x * vector2.x + vector1.y * vector2.y;
    const mag1 = Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y);
    const mag2 = Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y);

    if (mag1 === 0 || mag2 === 0) return 180; // Default to straight angle

    const cosAngle = Math.max(-1, Math.min(1, dot / (mag1 * mag2)));
    const angleRad = Math.acos(cosAngle);
    return angleRad * (180 / Math.PI);
  };

  // COMPREHENSIVE ANGLE ANALYSIS
  const calculateAllBiomechanicalAngles = (skeleton: ProfessionalSkeleton): AnatomicalAngle[] => {
    return [
      {
        name: 'Hip Flexion',
        value: calculatePreciseInternalAngle(skeleton.t12, skeleton.hip, skeleton.knee),
        vertex: skeleton.hip,
        point1: skeleton.t12,
        point2: skeleton.knee,
        significance: 'primary',
        confidence: (skeleton.hip.confidence + skeleton.knee.confidence) / 2
      },
      {
        name: 'Knee Flexion',
        value: calculatePreciseInternalAngle(skeleton.hip, skeleton.knee, skeleton.ankle),
        vertex: skeleton.knee,
        point1: skeleton.hip,
        point2: skeleton.ankle,
        significance: 'primary',
        confidence: (skeleton.hip.confidence + skeleton.knee.confidence + skeleton.ankle.confidence) / 3
      },
      {
        name: 'Ankle Dorsiflexion',
        value: calculatePreciseInternalAngle(skeleton.knee, skeleton.ankle, skeleton.forefoot),
        vertex: skeleton.ankle,
        point1: skeleton.knee,
        point2: skeleton.forefoot,
        significance: 'primary',
        confidence: (skeleton.knee.confidence + skeleton.ankle.confidence + skeleton.forefoot.confidence) / 3
      },
      {
        name: 'Shoulder Flexion',
        value: calculatePreciseInternalAngle(skeleton.c7, skeleton.shoulder, skeleton.elbow),
        vertex: skeleton.shoulder,
        point1: skeleton.c7,
        point2: skeleton.elbow,
        significance: 'secondary',
        confidence: (skeleton.shoulder.confidence + skeleton.elbow.confidence) / 2
      },
      {
        name: 'Elbow Flexion',
        value: calculatePreciseInternalAngle(skeleton.shoulder, skeleton.elbow, skeleton.wrist),
        vertex: skeleton.elbow,
        point1: skeleton.shoulder,
        point2: skeleton.wrist,
        significance: 'secondary',
        confidence: (skeleton.shoulder.confidence + skeleton.elbow.confidence + skeleton.wrist.confidence) / 3
      },
      {
        name: 'Trunk Angle',
        value: 90 - Math.abs(Math.atan2(skeleton.hip.x - skeleton.c7.x, skeleton.hip.y - skeleton.c7.y) * 180 / Math.PI),
        vertex: skeleton.c7,
        point1: skeleton.head,
        point2: skeleton.hip,
        significance: 'secondary',
        confidence: (skeleton.c7.confidence + skeleton.hip.confidence) / 2
      }
    ];
  };

  // PROFESSIONAL ANGLE ARC GENERATION aligned with bone vectors
  const createProfessionalAngleArc = (angle: AnatomicalAngle, radius: number): string => {
    const { vertex, point1, point2, value } = angle;

    // Calculate unit vectors from vertex to both points
    const vec1 = {
      x: point1.x - vertex.x,
      y: point1.y - vertex.y
    };
    const vec2 = {
      x: point2.x - vertex.x,
      y: point2.y - vertex.y
    };

    // Normalize vectors
    const mag1 = Math.sqrt(vec1.x * vec1.x + vec1.y * vec1.y);
    const mag2 = Math.sqrt(vec2.x * vec2.x + vec2.y * vec2.y);

    if (mag1 === 0 || mag2 === 0) return '';

    vec1.x /= mag1; vec1.y /= mag1;
    vec2.x /= mag2; vec2.y /= mag2;

    // Calculate arc endpoints
    const arcStart = {
      x: vertex.x + vec1.x * radius,
      y: vertex.y + vec1.y * radius
    };

    const arcEnd = {
      x: vertex.x + vec2.x * radius,
      y: vertex.y + vec2.y * radius
    };

    // Determine sweep direction using cross product
    const cross = vec1.x * vec2.y - vec1.y * vec2.x;
    const sweepFlag = cross > 0 ? 1 : 0;
    const largeArcFlag = value > 180 ? 1 : 0;

    return `M ${vertex.x} ${vertex.y} L ${arcStart.x} ${arcStart.y} A ${radius} ${radius} 0 ${largeArcFlag} ${sweepFlag} ${arcEnd.x} ${arcEnd.y} Z`;
  };

  // FIXED ADAPTIVE SCALING SYSTEM - Proper scaling for SVG viewBox 0-100
  const calculateAdaptiveScaling = (skeleton: ProfessionalSkeleton) => {
    // CRITICAL FIX: Use fixed scaling appropriate for SVG viewBox="0 0 100 100"
    // The coordinates are already normalized to 0-100, so we don't need complex scaling

    const bodyHeight = Math.abs(skeleton.head.y - skeleton.ankle.y);
    const expectedBodyHeight = 75; // Expected percentage of video height

    // Debug the body height calculation
    if (currentPose && currentPose.frame_number % 30 === 0) {
      const videoElement = videoRef?.current;
      console.log(`🔧 Scaling Debug Frame ${currentPose.frame_number}:`, {
        bodyHeight: bodyHeight.toFixed(1),
        expectedBodyHeight,
        head_y: skeleton.head.y.toFixed(1),
        ankle_y: skeleton.ankle.y.toFixed(1),
        scale_calculation: `${expectedBodyHeight} / ${bodyHeight.toFixed(1)} = ${(expectedBodyHeight / bodyHeight).toFixed(2)}`,
        video_dimensions: videoElement ? {
          width: videoElement.videoWidth,
          height: videoElement.videoHeight,
          aspect_ratio: (videoElement.videoWidth / videoElement.videoHeight).toFixed(2)
        } : 'No video element'
      });
    }

    // Use a more conservative scale calculation
    const scale = bodyHeight > 0 ? Math.max(0.8, Math.min(1.5, expectedBodyHeight / bodyHeight)) : 1.0;

    // REFINED: Subtle and precise scaling for joint tracking verification
    // These values are optimized for clear visibility without obstructing the video
    return {
      strokeWidth: Math.max(0.3, 0.5 * scale), // Thin, precise skeletal lines
      primaryJointRadius: Math.max(0.8, 1.2 * scale), // Small, precise joint markers
      secondaryJointRadius: Math.max(0.5, 0.8 * scale), // Even smaller secondary joints
      fontSize: Math.max(1.8, 2.2 * scale), // Smaller, minimally obtrusive angle labels
      arcRadius: Math.max(3, 4 * scale), // Small arc radius just to show angle origin
      labelWidth: Math.max(20, 25 * scale), // Compact label width
      labelHeight: Math.max(12, 15 * scale), // Compact label height
      scale: scale
    };
  };

  const renderProfessionalRunningAnalysis = () => {
    if (!currentPose || !currentPose.pose_detected) {
      return (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-black bg-opacity-75 text-white px-6 py-3 rounded-lg border border-gray-400">
            <div className="flex items-center space-x-3">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              <span className="text-sm font-medium">
                {isLoading ? 'Loading biomechanical data...' : 'No pose detected in current frame'}
              </span>
            </div>
          </div>
        </div>
      );
    }

    // Build professional skeleton with anatomical constraints
    const skeleton = applyAnatomicalConstraints(currentPose);
    const angles = calculateAllBiomechanicalAngles(skeleton);
    const scaling = calculateAdaptiveScaling(skeleton);

    // CRITICAL DEBUG: Log final skeleton coordinates used for SVG rendering
    if (currentPose.frame_number % 30 === 0) { // Log every 30th frame to avoid spam
      console.log(`🎯 SkeletalOverlay Frame ${currentPose.frame_number} - Final Skeleton Coordinates for SVG:`, {
        hip: { x: skeleton.hip.x.toFixed(1), y: skeleton.hip.y.toFixed(1) },
        knee: { x: skeleton.knee.x.toFixed(1), y: skeleton.knee.y.toFixed(1) },
        ankle: { x: skeleton.ankle.x.toFixed(1), y: skeleton.ankle.y.toFixed(1) },
        trunk: { x: skeleton.c7.x.toFixed(1), y: skeleton.c7.y.toFixed(1) },
        neck: { x: skeleton.neck.x.toFixed(1), y: skeleton.neck.y.toFixed(1) },
        svg_viewbox: '0 0 100 100',
        coordinate_validation: {
          hip_in_range: skeleton.hip.x >= 0 && skeleton.hip.x <= 100 && skeleton.hip.y >= 0 && skeleton.hip.y <= 100,
          knee_in_range: skeleton.knee.x >= 0 && skeleton.knee.x <= 100 && skeleton.knee.y >= 0 && skeleton.knee.y <= 100,
          ankle_in_range: skeleton.ankle.x >= 0 && skeleton.ankle.x <= 100 && skeleton.ankle.y >= 0 && skeleton.ankle.y <= 100
        },
        scaling_info: {
          bodyScale: scaling.scale.toFixed(2),
          strokeWidth: scaling.strokeWidth.toFixed(1),
          jointRadius: scaling.primaryJointRadius.toFixed(1)
        }
      });
    }

    // Calculate dynamic viewBox based on actual video aspect ratio for enhanced coordinate accuracy
    const videoElement = videoRef?.current;
    const videoAspectRatio = videoElement && videoElement.videoWidth && videoElement.videoHeight ?
      videoElement.videoHeight / videoElement.videoWidth :
      1920 / 1080; // Fallback for landscape videos

    const viewBoxHeight = 100 * videoAspectRatio; // e.g., ~177.8 for portrait iPhone videos (1080x1920)

    // Debug logging for dynamic viewBox calculation
    if (currentPose && currentPose.frame_number % 30 === 0) {
      console.log(`📐 Dynamic ViewBox Frame ${currentPose.frame_number}:`, {
        video_dimensions: videoElement ? {
          width: videoElement.videoWidth,
          height: videoElement.videoHeight,
          aspect_ratio: videoAspectRatio.toFixed(3)
        } : 'No video element',
        viewBox_calculation: {
          width: 100,
          height: viewBoxHeight.toFixed(1),
          viewBox_string: `0 0 100 ${viewBoxHeight.toFixed(1)}`,
          coordinate_system: videoAspectRatio > 1 ? 'PORTRAIT' : 'LANDSCAPE'
        },
        coordinate_mapping: 'Perfect 1:1 mapping between database coordinates and video display'
      });
    }

    return (
      <svg
        className="absolute inset-0 w-full h-full pointer-events-none"
        viewBox={`0 0 100 ${viewBoxHeight}`}
        preserveAspectRatio="none"
        style={{ zIndex: 50 }}
      >
        <defs>
          {/* Professional filters for medical-grade visualization */}
          <filter id="professionalGlow">
            <feGaussianBlur stdDeviation="0.3" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="professionalShadow">
            <feDropShadow dx="0.1" dy="0.1" stdDeviation="0.2" floodColor="black" floodOpacity="0.4"/>
          </filter>
          <filter id="labelShadow">
            <feDropShadow dx="0.2" dy="0.2" stdDeviation="0.4" floodColor="black" floodOpacity="0.6"/>
          </filter>

          {/* Gradient definitions for professional appearance */}
          <linearGradient id="primaryJointGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#FF4444" stopOpacity="1"/>
            <stop offset="100%" stopColor="#CC0000" stopOpacity="1"/>
          </linearGradient>
          <linearGradient id="secondaryJointGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#4444FF" stopOpacity="0.8"/>
            <stop offset="100%" stopColor="#0000CC" stopOpacity="0.8"/>
          </linearGradient>
        </defs>

        {/* COMPREHENSIVE PROFESSIONAL SKELETON */}
        <g className="professional-skeleton">

          {/* CENTRAL SPINE - anatomically accurate vertebral column */}
          <g className="spinal-column">
            <line x1={skeleton.head.x} y1={skeleton.head.y} x2={skeleton.neck.x} y2={skeleton.neck.y}
                  stroke="#0066CC" strokeWidth={scaling.strokeWidth * 1.4} strokeLinecap="round"
                  opacity="0.95" filter="url(#professionalGlow)"/>
            <line x1={skeleton.neck.x} y1={skeleton.neck.y} x2={skeleton.c7.x} y2={skeleton.c7.y}
                  stroke="#0066CC" strokeWidth={scaling.strokeWidth * 1.6} strokeLinecap="round"
                  opacity="0.95" filter="url(#professionalGlow)"/>
            <line x1={skeleton.c7.x} y1={skeleton.c7.y} x2={skeleton.t12.x} y2={skeleton.t12.y}
                  stroke="#0066CC" strokeWidth={scaling.strokeWidth * 1.8} strokeLinecap="round"
                  opacity="0.95" filter="url(#professionalGlow)"/>
            <line x1={skeleton.t12.x} y1={skeleton.t12.y} x2={skeleton.l5.x} y2={skeleton.l5.y}
                  stroke="#0066CC" strokeWidth={scaling.strokeWidth * 1.6} strokeLinecap="round"
                  opacity="0.95" filter="url(#professionalGlow)"/>
            <line x1={skeleton.l5.x} y1={skeleton.l5.y} x2={skeleton.sacrum.x} y2={skeleton.sacrum.y}
                  stroke="#0066CC" strokeWidth={scaling.strokeWidth * 1.4} strokeLinecap="round"
                  opacity="0.95" filter="url(#professionalGlow)"/>
          </g>

          {/* PELVIC GIRDLE */}
          <g className="pelvic-girdle">
            <line x1={skeleton.sacrum.x} y1={skeleton.sacrum.y} x2={skeleton.hip.x} y2={skeleton.hip.y}
                  stroke="#0066CC" strokeWidth={scaling.strokeWidth * 1.5} strokeLinecap="round"
                  opacity="0.95" filter="url(#professionalGlow)"/>
            <line x1={skeleton.sacrum.x} y1={skeleton.sacrum.y} x2={skeleton.hipMirrored.x} y2={skeleton.hipMirrored.y}
                  stroke="#0066CC" strokeWidth={scaling.strokeWidth * 1.2} strokeLinecap="round"
                  opacity="0.75" filter="url(#professionalGlow)"/>
          </g>

          {/* PRIMARY LEG - detected side with high confidence */}
          <g className="primary-leg">
            <line x1={skeleton.hip.x} y1={skeleton.hip.y} x2={skeleton.knee.x} y2={skeleton.knee.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 1.8} strokeLinecap="round"
                  opacity="0.98" filter="url(#professionalGlow)"/>
            <line x1={skeleton.knee.x} y1={skeleton.knee.y} x2={skeleton.ankle.x} y2={skeleton.ankle.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 1.8} strokeLinecap="round"
                  opacity="0.98" filter="url(#professionalGlow)"/>

            {/* DETAILED FOOT STRUCTURE */}
            <line x1={skeleton.ankle.x} y1={skeleton.ankle.y} x2={skeleton.heel.x} y2={skeleton.heel.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 1.4} strokeLinecap="round"
                  opacity="0.90" filter="url(#professionalGlow)"/>
            <line x1={skeleton.heel.x} y1={skeleton.heel.y} x2={skeleton.forefoot.x} y2={skeleton.forefoot.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 1.3} strokeLinecap="round"
                  opacity="0.85" filter="url(#professionalGlow)"/>
            <line x1={skeleton.forefoot.x} y1={skeleton.forefoot.y} x2={skeleton.toes.x} y2={skeleton.toes.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 1.1} strokeLinecap="round"
                  opacity="0.80" filter="url(#professionalGlow)"/>
            <line x1={skeleton.ankle.x} y1={skeleton.ankle.y} x2={skeleton.forefoot.x} y2={skeleton.forefoot.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 1.0} strokeLinecap="round"
                  opacity="0.75" filter="url(#professionalGlow)"/>
          </g>

          {/* SECONDARY LEG - mirrored side with reduced opacity */}
          <g className="secondary-leg">
            <line x1={skeleton.hipMirrored.x} y1={skeleton.hipMirrored.y} x2={skeleton.kneeMirrored.x} y2={skeleton.kneeMirrored.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 1.2} strokeLinecap="round"
                  opacity="0.65" filter="url(#professionalGlow)"/>
            <line x1={skeleton.kneeMirrored.x} y1={skeleton.kneeMirrored.y} x2={skeleton.ankleMirrored.x} y2={skeleton.ankleMirrored.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 1.2} strokeLinecap="round"
                  opacity="0.65" filter="url(#professionalGlow)"/>

            {/* Mirrored foot structure */}
            <line x1={skeleton.ankleMirrored.x} y1={skeleton.ankleMirrored.y} x2={skeleton.heelMirrored.x} y2={skeleton.heelMirrored.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 1.0} strokeLinecap="round"
                  opacity="0.55" filter="url(#professionalGlow)"/>
            <line x1={skeleton.heelMirrored.x} y1={skeleton.heelMirrored.y} x2={skeleton.forefootMirrored.x} y2={skeleton.forefootMirrored.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 0.9} strokeLinecap="round"
                  opacity="0.50" filter="url(#professionalGlow)"/>
            <line x1={skeleton.forefootMirrored.x} y1={skeleton.forefootMirrored.y} x2={skeleton.toesMirrored.x} y2={skeleton.toesMirrored.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 0.8} strokeLinecap="round"
                  opacity="0.45" filter="url(#professionalGlow)"/>
            <line x1={skeleton.ankleMirrored.x} y1={skeleton.ankleMirrored.y} x2={skeleton.forefootMirrored.x} y2={skeleton.forefootMirrored.y}
                  stroke="#0088FF" strokeWidth={scaling.strokeWidth * 0.7} strokeLinecap="round"
                  opacity="0.40" filter="url(#professionalGlow)"/>
          </g>

          {/* SHOULDER GIRDLE AND ARMS */}
          <g className="upper-extremities">
            {/* Shoulder connections to spine */}
            <line x1={skeleton.c7.x} y1={skeleton.c7.y} x2={skeleton.shoulder.x} y2={skeleton.shoulder.y}
                  stroke="#0066CC" strokeWidth={scaling.strokeWidth * 1.3} strokeLinecap="round"
                  opacity={skeleton.shoulder.confidence} filter="url(#professionalGlow)"/>
            <line x1={skeleton.c7.x} y1={skeleton.c7.y} x2={skeleton.shoulderMirrored.x} y2={skeleton.shoulderMirrored.y}
                  stroke="#0066CC" strokeWidth={scaling.strokeWidth * 1.0} strokeLinecap="round"
                  opacity={skeleton.shoulderMirrored.confidence} filter="url(#professionalGlow)"/>

            {/* Primary arm */}
            <line x1={skeleton.shoulder.x} y1={skeleton.shoulder.y} x2={skeleton.elbow.x} y2={skeleton.elbow.y}
                  stroke="#00AA55" strokeWidth={scaling.strokeWidth * 1.4} strokeLinecap="round"
                  opacity={skeleton.elbow.confidence} filter="url(#professionalGlow)"/>
            <line x1={skeleton.elbow.x} y1={skeleton.elbow.y} x2={skeleton.wrist.x} y2={skeleton.wrist.y}
                  stroke="#00AA55" strokeWidth={scaling.strokeWidth * 1.2} strokeLinecap="round"
                  opacity={skeleton.wrist.confidence} filter="url(#professionalGlow)"/>
            <line x1={skeleton.wrist.x} y1={skeleton.wrist.y} x2={skeleton.hand.x} y2={skeleton.hand.y}
                  stroke="#00AA55" strokeWidth={scaling.strokeWidth * 1.0} strokeLinecap="round"
                  opacity={skeleton.hand.confidence} filter="url(#professionalGlow)"/>

            {/* Secondary arm */}
            <line x1={skeleton.shoulderMirrored.x} y1={skeleton.shoulderMirrored.y} x2={skeleton.elbowMirrored.x} y2={skeleton.elbowMirrored.y}
                  stroke="#00AA55" strokeWidth={scaling.strokeWidth * 1.0} strokeLinecap="round"
                  opacity={skeleton.elbowMirrored.confidence} filter="url(#professionalGlow)"/>
            <line x1={skeleton.elbowMirrored.x} y1={skeleton.elbowMirrored.y} x2={skeleton.wristMirrored.x} y2={skeleton.wristMirrored.y}
                  stroke="#00AA55" strokeWidth={scaling.strokeWidth * 0.9} strokeLinecap="round"
                  opacity={skeleton.wristMirrored.confidence} filter="url(#professionalGlow)"/>
            <line x1={skeleton.wristMirrored.x} y1={skeleton.wristMirrored.y} x2={skeleton.handMirrored.x} y2={skeleton.handMirrored.y}
                  stroke="#00AA55" strokeWidth={scaling.strokeWidth * 0.8} strokeLinecap="round"
                  opacity={skeleton.handMirrored.confidence} filter="url(#professionalGlow)"/>
          </g>
        </g>

        {/* PROFESSIONAL ANGLE ARCS - precisely aligned with bone segments */}
        <g className="biomechanical-angle-arcs">
          {angles.filter(angle => angle.significance === 'primary' && angle.confidence > 0.7).map((angle, index) => (
            <path key={`primary-arc-${index}`}
                  d={createProfessionalAngleArc(angle, scaling.arcRadius * (angle.significance === 'primary' ? 1.0 : 0.8))}
                  fill="rgba(255,165,0,0.5)"
                  stroke="rgba(255,140,0,0.95)"
                  strokeWidth="0.8"
                  opacity={angle.confidence}
                  filter="url(#professionalShadow)"/>
          ))}
          {angles.filter(angle => angle.significance === 'secondary' && angle.confidence > 0.6).map((angle, index) => (
            <path key={`secondary-arc-${index}`}
                  d={createProfessionalAngleArc(angle, scaling.arcRadius * 0.7)}
                  fill="rgba(255,165,0,0.3)"
                  stroke="rgba(255,140,0,0.8)"
                  strokeWidth="0.6"
                  opacity={angle.confidence * 0.8}
                  filter="url(#professionalShadow)"/>
          ))}
        </g>

        {/* PROFESSIONAL JOINT MARKERS - anatomically sized and positioned */}
        <g className="anatomical-joint-markers">
          {/* SPINE JOINTS */}
          <circle cx={skeleton.head.x} cy={skeleton.head.y} r={scaling.secondaryJointRadius * 0.8}
                  fill="#87CEEB" stroke="white" strokeWidth={scaling.strokeWidth * 0.3}
                  opacity="0.9" filter="url(#professionalShadow)"/>
          <circle cx={skeleton.neck.x} cy={skeleton.neck.y} r={scaling.secondaryJointRadius}
                  fill="#0066CC" stroke="white" strokeWidth={scaling.strokeWidth * 0.3}
                  opacity="0.95" filter="url(#professionalShadow)"/>
          <circle cx={skeleton.c7.x} cy={skeleton.c7.y} r={scaling.secondaryJointRadius * 0.9}
                  fill="#0066CC" stroke="white" strokeWidth={scaling.strokeWidth * 0.25}
                  opacity="0.9" filter="url(#professionalShadow)"/>
          <circle cx={skeleton.t12.x} cy={skeleton.t12.y} r={scaling.secondaryJointRadius * 0.8}
                  fill="#0066CC" stroke="white" strokeWidth={scaling.strokeWidth * 0.2}
                  opacity="0.85" filter="url(#professionalShadow)"/>
          <circle cx={skeleton.l5.x} cy={skeleton.l5.y} r={scaling.secondaryJointRadius * 0.8}
                  fill="#0066CC" stroke="white" strokeWidth={scaling.strokeWidth * 0.2}
                  opacity="0.85" filter="url(#professionalShadow)"/>
          <circle cx={skeleton.sacrum.x} cy={skeleton.sacrum.y} r={scaling.secondaryJointRadius * 0.9}
                  fill="#0066CC" stroke="white" strokeWidth={scaling.strokeWidth * 0.25}
                  opacity="0.9" filter="url(#professionalShadow)"/>

          {/* PRIMARY LEG JOINTS */}
          <circle cx={skeleton.hip.x} cy={skeleton.hip.y} r={scaling.primaryJointRadius}
                  fill="url(#primaryJointGradient)" stroke="white" strokeWidth={scaling.strokeWidth * 0.4}
                  opacity="0.98" filter="url(#professionalShadow)"/>
          <circle cx={skeleton.knee.x} cy={skeleton.knee.y} r={scaling.primaryJointRadius * 1.1}
                  fill="#FFDD00" stroke="white" strokeWidth={scaling.strokeWidth * 0.4}
                  opacity="0.98" filter="url(#professionalShadow)"/>
          <circle cx={skeleton.ankle.x} cy={skeleton.ankle.y} r={scaling.primaryJointRadius}
                  fill="#00FF44" stroke="white" strokeWidth={scaling.strokeWidth * 0.4}
                  opacity="0.95" filter="url(#professionalShadow)"/>

          {/* FOOT DETAIL JOINTS */}
          <circle cx={skeleton.heel.x} cy={skeleton.heel.y} r={scaling.secondaryJointRadius * 0.7}
                  fill="#00CC33" stroke="white" strokeWidth={scaling.strokeWidth * 0.2}
                  opacity={skeleton.heel.confidence} filter="url(#professionalShadow)"/>
          <circle cx={skeleton.forefoot.x} cy={skeleton.forefoot.y} r={scaling.secondaryJointRadius * 0.6}
                  fill="#00AA22" stroke="white" strokeWidth={scaling.strokeWidth * 0.15}
                  opacity={skeleton.forefoot.confidence} filter="url(#professionalShadow)"/>
          <circle cx={skeleton.toes.x} cy={skeleton.toes.y} r={scaling.secondaryJointRadius * 0.5}
                  fill="#008811" stroke="white" strokeWidth={scaling.strokeWidth * 0.1}
                  opacity={skeleton.toes.confidence} filter="url(#professionalShadow)"/>

          {/* SECONDARY LEG JOINTS */}
          <circle cx={skeleton.hipMirrored.x} cy={skeleton.hipMirrored.y} r={scaling.secondaryJointRadius * 0.8}
                  fill="url(#secondaryJointGradient)" stroke="white" strokeWidth={scaling.strokeWidth * 0.25}
                  opacity="0.7" filter="url(#professionalShadow)"/>
          <circle cx={skeleton.kneeMirrored.x} cy={skeleton.kneeMirrored.y} r={scaling.secondaryJointRadius * 0.8}
                  fill="#CCAA00" stroke="white" strokeWidth={scaling.strokeWidth * 0.25}
                  opacity="0.7" filter="url(#professionalShadow)"/>
          <circle cx={skeleton.ankleMirrored.x} cy={skeleton.ankleMirrored.y} r={scaling.secondaryJointRadius * 0.7}
                  fill="#00CC22" stroke="white" strokeWidth={scaling.strokeWidth * 0.2}
                  opacity="0.65" filter="url(#professionalShadow)"/>

          {/* ARM JOINTS */}
          <circle cx={skeleton.shoulder.x} cy={skeleton.shoulder.y} r={scaling.secondaryJointRadius * 0.9}
                  fill="#FF8800" stroke="white" strokeWidth={scaling.strokeWidth * 0.25}
                  opacity={skeleton.shoulder.confidence} filter="url(#professionalShadow)"/>
          <circle cx={skeleton.elbow.x} cy={skeleton.elbow.y} r={scaling.secondaryJointRadius * 0.8}
                  fill="#FFAA00" stroke="white" strokeWidth={scaling.strokeWidth * 0.2}
                  opacity={skeleton.elbow.confidence} filter="url(#professionalShadow)"/>
          <circle cx={skeleton.wrist.x} cy={skeleton.wrist.y} r={scaling.secondaryJointRadius * 0.7}
                  fill="#FFCC00" stroke="white" strokeWidth={scaling.strokeWidth * 0.15}
                  opacity={skeleton.wrist.confidence} filter="url(#professionalShadow)"/>
          <circle cx={skeleton.hand.x} cy={skeleton.hand.y} r={scaling.secondaryJointRadius * 0.6}
                  fill="#FFDD44" stroke="white" strokeWidth={scaling.strokeWidth * 0.1}
                  opacity={skeleton.hand.confidence} filter="url(#professionalShadow)"/>
        </g>

        {/* DYNAMIC PROFESSIONAL ANGLE LABELS */}
        <g className="professional-angle-labels">
          {angles.filter(angle => angle.confidence > 0.7).map((angle, index) => {
            // Calculate dynamic label dimensions based on angle text content
            const angleText = Math.round(angle.value).toString() + '°';

            // More precise character width calculation for different angle ranges
            const characterWidth = scaling.fontSize * 0.65; // Refined character width ratio
            const degreeSymbolWidth = scaling.fontSize * 0.4; // Degree symbol is narrower
            const textWidth = (angleText.length - 1) * characterWidth + degreeSymbolWidth;

            // Dynamic padding based on font size with minimum and maximum bounds
            const basePadding = scaling.fontSize * 0.8;
            const minPadding = Math.max(4, basePadding); // Minimum 4 units padding
            const maxPadding = Math.min(12, basePadding); // Maximum 12 units padding
            const padding = Math.max(minPadding, Math.min(maxPadding, basePadding));

            // Calculate final dimensions with safety bounds
            const dynamicLabelWidth = Math.max(textWidth + padding, scaling.fontSize * 2); // Minimum width
            const labelHeight = scaling.fontSize * 1.4; // Dynamic height based on font size

            // Debug logging for dynamic label sizing (every 30th frame to avoid spam)
            if (currentPose && currentPose.frame_number % 30 === 0 && index === 0) {
              console.log(`Frame ${currentPose.frame_number} - Dynamic label sizing:`, {
                angleText,
                textWidth: textWidth.toFixed(1),
                padding: padding.toFixed(1),
                dynamicLabelWidth: dynamicLabelWidth.toFixed(1),
                fontSize: scaling.fontSize.toFixed(1),
                comparison: `Fixed: ${scaling.labelWidth?.toFixed(1) || 'N/A'} vs Dynamic: ${dynamicLabelWidth.toFixed(1)}`
              });
            }

            // Use dynamic width for label positioning and sizing
            const labelX = angle.vertex.x - (dynamicLabelWidth / 2);
            const labelY = angle.vertex.y - labelHeight - (scaling.arcRadius * 1.5) - (index * 2);

            return (
              <g key={`angle-label-${index}`}>
                <rect x={labelX} y={labelY}
                      width={dynamicLabelWidth} height={labelHeight}
                      rx="3" ry="3"
                      fill="rgba(255,140,0,0.95)"
                      stroke="white"
                      strokeWidth="0.4"
                      filter="url(#labelShadow)"/>
                <text x={angle.vertex.x} y={labelY + (labelHeight * 0.7)}
                      textAnchor="middle"
                      fill="white"
                      fontSize={scaling.fontSize}
                      fontWeight="bold"
                      fontFamily="Arial, sans-serif">
                  {angleText}
                </text>
              </g>
            );
          })}
        </g>
      </svg>
    );
  };

  return (
    <div className="absolute inset-0 pointer-events-none" style={{ zIndex: 20 }}>
      {error && (
        <div className="absolute top-2 left-2 bg-red-500 text-white px-3 py-2 rounded text-sm z-30">
          {error}
        </div>
      )}

      {activity === 'running' && view === 'side' && renderProfessionalRunningAnalysis()}
    </div>
  );
};

export default SkeletalOverlay;